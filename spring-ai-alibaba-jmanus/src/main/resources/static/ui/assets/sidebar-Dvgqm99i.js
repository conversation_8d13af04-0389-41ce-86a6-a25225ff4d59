var u=Object.defineProperty;var m=(n,e,t)=>e in n?u(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t;var r=(n,e,t)=>m(n,typeof e!="symbol"?e+"":e,t);import{E as T,r as h,s as g,G as p}from"./index-F6gaFRaU.js";const I=T("task",()=>{const n=h(null),e=h(""),t=h(!1);return{currentTask:n,taskToInput:e,hasVisitedHome:t,setTask:o=>{console.log("[TaskStore] setTask called with prompt:",o);const d={prompt:o,timestamp:Date.now(),processed:!1};n.value=d,console.log("[TaskStore] Task set, currentTask.value:",n.value)},setTaskToInput:o=>{console.log("[TaskStore] setTaskToInput called with prompt:",o),e.value=o,console.log("[TaskStore] Task to input set:",e.value)},getAndClearTaskToInput:()=>{const o=e.value;return e.value="",console.log("[TaskStore] getAndClearTaskToInput returning:",o),o},markTaskAsProcessed:()=>{console.log("[TaskStore] markTaskAsProcessed called, current task:",n.value),n.value?(n.value.processed=!0,console.log("[TaskStore] Task marked as processed:",n.value)):console.log("[TaskStore] No current task to mark as processed")},clearTask:()=>{n.value=null},hasUnprocessedTask:()=>{const o=n.value&&!n.value.processed;return console.log("[TaskStore] hasUnprocessedTask check - currentTask:",n.value,"result:",o),o},markHomeVisited:()=>{t.value=!0,localStorage.setItem("hasVisitedHome","true")},checkHomeVisited:()=>{const o=localStorage.getItem("hasVisitedHome");return t.value=o==="true",t.value},resetHomeVisited:()=>{t.value=!1,localStorage.removeItem("hasVisitedHome")},emitPlanExecutionRequested:o=>{console.log("[TaskStore] emitPlanExecutionRequested called with payload:",o),window.dispatchEvent(new CustomEvent("plan-execution-requested",{detail:o}))}}});class c{static async generatePlan(e,t){const s={query:e};t&&(s.existingJson=t);const a=await fetch(`${this.PLAN_TEMPLATE_URL}/generate`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(!a.ok)throw new Error(`Failed to generate plan: ${a.status}`);const i=await a.json();if(i.planJson)try{i.plan=JSON.parse(i.planJson)}catch{i.plan={error:"Unable to parse plan data"}}return i}static async executePlan(e,t){console.log("[PlanActApiService] executePlan called with:",{planTemplateId:e,rawParam:t});const s={planTemplateId:e};t&&(s.rawParam=t),console.log("[PlanActApiService] Making request to:",`${this.PLAN_TEMPLATE_URL}/executePlanByTemplateId`),console.log("[PlanActApiService] Request body:",s);const a=await fetch(`${this.PLAN_TEMPLATE_URL}/executePlanByTemplateId`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(console.log("[PlanActApiService] Response status:",a.status,a.ok),!a.ok){const l=await a.text();throw console.error("[PlanActApiService] Request failed:",l),new Error(`Failed to execute plan: ${a.status}`)}const i=await a.json();return console.log("[PlanActApiService] executePlan response:",i),i}static async savePlanTemplate(e,t){const s=await fetch(`${this.PLAN_TEMPLATE_URL}/save`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({planId:e,planJson:t})});if(!s.ok)throw new Error(`Failed to save plan: ${s.status}`);return await s.json()}static async getPlanVersions(e){const t=await fetch(`${this.PLAN_TEMPLATE_URL}/versions`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({planId:e})});if(!t.ok)throw new Error(`Failed to get plan versions: ${t.status}`);return await t.json()}static async getVersionPlan(e,t){const s=await fetch(`${this.PLAN_TEMPLATE_URL}/get-version`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({planId:e,versionIndex:t.toString()})});if(!s.ok)throw new Error(`Failed to get specific version plan: ${s.status}`);return await s.json()}static async getAllPlanTemplates(){const e=await fetch(`${this.PLAN_TEMPLATE_URL}/list`);if(!e.ok)throw new Error(`Failed to get plan template list: ${e.status}`);return await e.json()}static async updatePlanTemplate(e,t,s){const a={planId:e,query:t};s&&(a.existingJson=s);const i=await fetch(`${this.PLAN_TEMPLATE_URL}/update`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!i.ok)throw new Error(`Failed to update plan template: ${i.status}`);const l=await i.json();if(l.planJson)try{l.plan=JSON.parse(l.planJson)}catch{l.plan={error:"Unable to parse plan data"}}return l}static async deletePlanTemplate(e){const t=await fetch(`${this.PLAN_TEMPLATE_URL}/delete`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({planId:e})});if(!t.ok)throw new Error(`Failed to delete plan template: ${t.status}`);return await t.json()}static async createCronTask(e){const t=await fetch(this.CRON_TASK_URL,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)try{const s=await t.json();throw new Error(s.message||`Failed to create cron task: ${t.status}`)}catch{throw new Error(`Failed to create cron task: ${t.status}`)}return await t.json()}}r(c,"PLAN_TEMPLATE_URL","/api/plan-template"),r(c,"CRON_TASK_URL","/api/cron-tasks");class P{constructor(){r(this,"isCollapsed",!0);r(this,"currentTab","list");r(this,"currentPlanTemplateId",null);r(this,"planTemplateList",[]);r(this,"selectedTemplate",null);r(this,"isLoading",!1);r(this,"errorMessage","");r(this,"jsonContent","");r(this,"generatorPrompt","");r(this,"executionParams","");r(this,"isGenerating",!1);r(this,"isExecuting",!1);r(this,"planVersions",[]);r(this,"currentVersionIndex",-1)}get sortedTemplates(){return[...this.planTemplateList].sort((e,t)=>{const s=new Date(e.updateTime??e.createTime);return new Date(t.updateTime??t.createTime).getTime()-s.getTime()})}get canRollback(){return this.planVersions.length>1&&this.currentVersionIndex>0}get canRestore(){return this.planVersions.length>1&&this.currentVersionIndex<this.planVersions.length-1}get computedApiUrl(){if(!this.selectedTemplate)return"";const e=`/api/plan-template/execute/${this.selectedTemplate.id}`,t=this.executionParams.trim();return t?`${e}?allParams=${encodeURIComponent(t)}`:e}toggleSidebar(){this.isCollapsed=!this.isCollapsed}switchToTab(e){this.currentTab=e}async loadPlanTemplateList(){this.isLoading=!0,this.errorMessage="";try{console.log("[SidebarStore] Starting to load plan template list...");const e=await c.getAllPlanTemplates();e!=null&&e.templates&&Array.isArray(e.templates)?(this.planTemplateList=e.templates,console.log(`[SidebarStore] Successfully loaded ${e.templates.length} plan templates`)):(this.planTemplateList=[],console.warn("[SidebarStore] API returned abnormal data format, using empty list",e))}catch(e){console.error("[SidebarStore] Failed to load plan template list:",e),this.planTemplateList=[],this.errorMessage=`Load failed: ${e.message}`}finally{this.isLoading=!1}}async selectTemplate(e){this.currentPlanTemplateId=e.id,this.selectedTemplate=e,this.currentTab="config",await this.loadTemplateData(e),console.log(`[SidebarStore] Selected plan template: ${e.id}`)}async loadTemplateData(e){try{const t=await c.getPlanVersions(e.id);if(this.planVersions=t.versions||[],this.planVersions.length>0){const s=this.planVersions[this.planVersions.length-1];this.jsonContent=s,this.currentVersionIndex=this.planVersions.length-1;try{const a=JSON.parse(s);a.prompt&&(this.generatorPrompt=a.prompt),a.params&&(this.executionParams=a.params)}catch{console.warn("Unable to parse JSON content to get prompt information")}}else this.jsonContent="",this.generatorPrompt="",this.executionParams=""}catch(t){throw console.error("Failed to load template data:",t),t}}createNewTemplate(){const e={id:`new-${Date.now()}`,title:p.global.t("sidebar.newTemplateName"),description:p.global.t("sidebar.newTemplateDescription"),createTime:new Date().toISOString(),updateTime:new Date().toISOString()};this.selectedTemplate=e,this.currentPlanTemplateId=null,this.jsonContent="",this.generatorPrompt="",this.executionParams="",this.planVersions=[],this.currentVersionIndex=-1,this.currentTab="config",console.log("[SidebarStore] Created new empty plan template, switching to config tab")}async deleteTemplate(e){if(!e.id){console.warn("[SidebarStore] deleteTemplate: Invalid template object or ID");return}try{await c.deletePlanTemplate(e.id),this.currentPlanTemplateId===e.id&&this.clearSelection(),await this.loadPlanTemplateList(),console.log(`[SidebarStore] Plan template ${e.id} has been deleted`)}catch(t){throw console.error("Failed to delete plan template:",t),await this.loadPlanTemplateList(),t}}clearSelection(){this.currentPlanTemplateId=null,this.selectedTemplate=null,this.jsonContent="",this.generatorPrompt="",this.executionParams="",this.planVersions=[],this.currentVersionIndex=-1,this.currentTab="list"}clearExecutionParams(){this.executionParams=""}rollbackVersion(){this.canRollback&&(this.currentVersionIndex--,this.jsonContent=this.planVersions[this.currentVersionIndex])}restoreVersion(){this.canRestore&&(this.currentVersionIndex++,this.jsonContent=this.planVersions[this.currentVersionIndex])}async saveTemplate(){if(!this.selectedTemplate)return;const e=this.jsonContent.trim();if(!e)throw new Error("Content cannot be empty");try{JSON.parse(e)}catch(t){throw new Error(`Invalid format, please correct and save.
Error: `+t.message)}try{const t=await c.savePlanTemplate(this.selectedTemplate.id,e);return this.currentVersionIndex<this.planVersions.length-1&&(this.planVersions=this.planVersions.slice(0,this.currentVersionIndex+1)),this.planVersions.push(e),this.currentVersionIndex=this.planVersions.length-1,t}catch(t){throw console.error("Failed to save plan template:",t),t}}async generatePlan(){if(this.generatorPrompt.trim()){this.isGenerating=!0;try{const e=await c.generatePlan(this.generatorPrompt);if(this.jsonContent=e.planJson||"",this.selectedTemplate&&this.selectedTemplate.id.startsWith("new-")){let t="New Plan Template";try{t=JSON.parse(e.planJson||"{}").title||t}catch{console.warn("Unable to parse plan JSON to get title")}this.selectedTemplate={id:e.planTemplateId,title:t,description:p.global.t("sidebar.generatedTemplateDescription"),createTime:new Date().toISOString(),updateTime:new Date().toISOString(),planJson:e.planJson},this.currentPlanTemplateId=e.planTemplateId,await this.loadPlanTemplateList()}return this.currentVersionIndex<this.planVersions.length-1&&(this.planVersions=this.planVersions.slice(0,this.currentVersionIndex+1)),this.planVersions.push(this.jsonContent),this.currentVersionIndex=this.planVersions.length-1,e}catch(e){throw console.error("Failed to generate plan:",e),e}finally{this.isGenerating=!1}}}async updatePlan(){if(!(!this.generatorPrompt.trim()||!this.jsonContent.trim())&&this.selectedTemplate){this.isGenerating=!0;try{const e=await c.updatePlanTemplate(this.selectedTemplate.id,this.generatorPrompt,this.jsonContent);return this.jsonContent=e.planJson||"",this.currentVersionIndex<this.planVersions.length-1&&(this.planVersions=this.planVersions.slice(0,this.currentVersionIndex+1)),this.planVersions.push(this.jsonContent),this.currentVersionIndex=this.planVersions.length-1,e}catch(e){throw console.error("Failed to update plan:",e),e}finally{this.isGenerating=!1}}}preparePlanExecution(){if(!this.selectedTemplate)return null;this.isExecuting=!0;try{let e;try{e=JSON.parse(this.jsonContent),e.planTemplateId=this.selectedTemplate.id}catch{e={planTemplateId:this.selectedTemplate.id,planId:this.selectedTemplate.id,title:this.selectedTemplate.title??p.global.t("sidebar.defaultExecutionPlanTitle"),steps:[{stepRequirement:"[BROWSER_AGENT] 访问百度搜索阿里巴巴的最新股价"},{stepRequirement:"[DEFAULT_AGENT] 提取和整理搜索结果中的股价信息"},{stepRequirement:"[TEXT_FILE_AGENT] 创建一个文本文件记录查询结果"},{stepRequirement:"[DEFAULT_AGENT] 向用户报告查询结果"}]}}return{title:this.selectedTemplate.title??e.title??"Execution Plan",planData:e,params:this.executionParams.trim()||void 0}}catch(e){throw console.error("Failed to prepare plan execution:",e),this.isExecuting=!1,e}}finishPlanExecution(){this.isExecuting=!1}}const A=g(new P);export{c as P,A as s,I as u};
