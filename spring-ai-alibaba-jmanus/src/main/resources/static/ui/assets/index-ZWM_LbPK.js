var lt=Object.defineProperty;var it=(G,l,s)=>l in G?lt(G,l,{enumerable:!0,configurable:!0,writable:!0,value:s}):G[l]=s;var ge=(G,l,s)=>it(G,typeof l!="symbol"?l+"":l,s);import{d as Pe,f as Ee,g as ke,h as Ce,c as g,o as h,m as ne,u as o,e,b as B,t as a,i as k,p as Z,F as me,j as fe,q as re,w as pe,v as be,s as st,r as x,l as se,x as we,a as de,T as Ie,y as De,z as _e,n as Me,A as Ae,B as ct,C as rt,k as ot,D as ut}from"./index-BSo9eWK3.js";import{I as $,_ as Se}from"./_plugin-vue_export-helper-B6wzie0r.js";import{s as f,P as xe,u as at}from"./sidebar-hJuoja5I.js";import{L as dt}from"./index-DxC_K1Fg.js";import{u as pt}from"./useToast-BrzN4a8A.js";const ht={class:"sidebar-content"},gt={class:"sidebar-content-header"},mt={class:"sidebar-content-title"},vt={class:"tab-switcher"},ft=["disabled"],bt={key:0,class:"tab-content"},kt={class:"new-task-section"},_t={class:"sidebar-content-list"},$t={key:0,class:"loading-state"},Pt={key:1,class:"error-state"},Ct={key:2,class:"empty-state"},St=["onClick"],yt={class:"task-icon"},Et={class:"task-details"},wt={class:"task-title"},Tt={class:"task-preview"},It={class:"task-time"},Dt={class:"task-actions"},xt=["title","onClick"],Rt={key:1,class:"tab-content config-tab"},At={key:0,class:"config-container"},Mt={class:"template-info-header"},Nt={class:"template-info"},Ut={class:"template-id"},Lt={class:"config-section"},Vt={class:"section-header"},qt={class:"generator-content"},Ft=["placeholder"],Ot={class:"generator-actions"},Bt=["disabled"],Wt=["disabled"],jt={class:"config-section"},Ht={class:"section-header"},Jt={class:"section-actions"},zt=["disabled","title"],Gt=["disabled","title"],Xt=["disabled"],Kt=["placeholder"],Qt={class:"config-section"},Yt={class:"section-header"},Zt={class:"execution-content"},en={class:"params-input-group"},tn={class:"params-help-text"},nn={class:"params-input-container"},sn=["placeholder"],on=["title"],an={class:"api-url-display"},ln={class:"api-url-label"},cn={class:"api-url"},rn={class:"api-url-display"},un={class:"api-url-label"},dn=["disabled"],pn=Pe({__name:"index",emits:["planExecutionRequested"],setup(G,{expose:l,emit:s}){const{t:u}=Ee(),L=["currentPlanId","userRequest","rootPlanId"],m=ke({get(){try{if(!f.jsonContent)return"";const C={...JSON.parse(f.jsonContent)};return L.forEach(E=>{delete C[E]}),JSON.stringify(C,null,2)}catch{return f.jsonContent}},set(r){try{if(!r.trim()){f.jsonContent="";return}const C=JSON.parse(r);let E={};try{E=JSON.parse(f.jsonContent||"{}")}catch{}const Q={...C};L.forEach(p=>{E[p]!==void 0&&(Q[p]=E[p])}),f.jsonContent=JSON.stringify(Q)}catch{f.jsonContent=r}}}),v=s,w=async()=>{try{const r=await f.saveTemplate();r!=null&&r.duplicate?alert(u("sidebar.saveCompleted",{message:r.message,versionCount:r.versionCount})):r!=null&&r.saved?alert(u("sidebar.saveSuccess",{message:r.message,versionCount:r.versionCount})):r!=null&&r.message&&alert(u("sidebar.saveStatus",{message:r.message}))}catch(r){console.error("保存计划修改失败:",r),alert(r.message||u("sidebar.saveFailed"))}},T=async()=>{var r;try{await f.generatePlan(),alert(u("sidebar.generateSuccess",{templateId:((r=f.selectedTemplate)==null?void 0:r.id)??u("sidebar.unknown")}))}catch(C){console.error("生成计划失败:",C),alert(u("sidebar.generateFailed")+": "+C.message)}},I=async()=>{try{await f.updatePlan(),alert(u("sidebar.updateSuccess"))}catch(r){console.error("更新计划失败:",r),alert(u("sidebar.updateFailed")+": "+r.message)}},Y=async()=>{console.log("[Sidebar] handleExecutePlan called");try{const r=f.preparePlanExecution();if(!r){console.log("[Sidebar] No plan data available, returning");return}console.log("[Sidebar] 触发计划执行请求:",r),console.log("[Sidebar] Emitting planExecutionRequested event"),v("planExecutionRequested",r),console.log("[Sidebar] Event emitted")}catch(r){console.error("执行计划出错:",r),alert(u("sidebar.executeFailed")+": "+r.message)}finally{f.finishPlanExecution()}},X=r=>{const E=new Date().getTime()-r.getTime(),Q=Math.floor(E/6e4),p=Math.floor(E/36e5),R=Math.floor(E/864e5);return Q<1?u("time.now"):Q<60?u("time.minuteAgo",{count:Q}):p<24?u("time.hourAgo",{count:p}):R<30?u("time.dayAgo",{count:R}):r.toLocaleDateString("zh-CN")},q=(r,C)=>!r||r.length<=C?r:r.substring(0,C)+"...";return Ce(()=>{f.loadPlanTemplateList()}),l({loadPlanTemplateList:f.loadPlanTemplateList,toggleSidebar:f.toggleSidebar,currentPlanTemplateId:f.currentPlanTemplateId}),(r,C)=>(h(),g("div",{class:ne(["sidebar-wrapper",{"sidebar-wrapper-collapsed":o(f).isCollapsed}])},[e("div",ht,[e("div",gt,[e("div",mt,a(r.$t("sidebar.title")),1)]),e("div",vt,[e("button",{class:ne(["tab-button",{active:o(f).currentTab==="list"}]),onClick:C[0]||(C[0]=E=>o(f).switchToTab("list"))},[k(o($),{icon:"carbon:list",width:"16"}),Z(" "+a(r.$t("sidebar.templateList")),1)],2),e("button",{class:ne(["tab-button",{active:o(f).currentTab==="config"}]),onClick:C[1]||(C[1]=E=>o(f).switchToTab("config")),disabled:!o(f).selectedTemplate},[k(o($),{icon:"carbon:settings",width:"16"}),Z(" "+a(r.$t("sidebar.configuration")),1)],10,ft)]),o(f).currentTab==="list"?(h(),g("div",bt,[e("div",kt,[e("button",{class:"new-task-btn",onClick:C[2]||(C[2]=E=>o(f).createNewTemplate())},[k(o($),{icon:"carbon:add",width:"16"}),Z(" "+a(r.$t("sidebar.newPlan"))+" ",1),C[11]||(C[11]=e("span",{class:"shortcut"},"⌘ K",-1))])]),e("div",_t,[o(f).isLoading?(h(),g("div",$t,[k(o($),{icon:"carbon:circle-dash",width:"20",class:"spinning"}),e("span",null,a(r.$t("sidebar.loading")),1)])):o(f).errorMessage?(h(),g("div",Pt,[k(o($),{icon:"carbon:warning",width:"20"}),e("span",null,a(o(f).errorMessage),1),e("button",{onClick:C[3]||(C[3]=(...E)=>o(f).loadPlanTemplateList&&o(f).loadPlanTemplateList(...E)),class:"retry-btn"},a(r.$t("sidebar.retry")),1)])):o(f).planTemplateList.length===0?(h(),g("div",Ct,[k(o($),{icon:"carbon:document",width:"32"}),e("span",null,a(r.$t("sidebar.noTemplates")),1)])):(h(!0),g(me,{key:3},fe(o(f).sortedTemplates,E=>(h(),g("div",{key:E.id,class:ne(["sidebar-content-list-item",{"sidebar-content-list-item-active":E.id===o(f).currentPlanTemplateId}]),onClick:Q=>o(f).selectTemplate(E)},[e("div",yt,[k(o($),{icon:"carbon:document",width:"20"})]),e("div",Et,[e("div",wt,a(E.title||r.$t("sidebar.unnamedPlan")),1),e("div",Tt,a(q(E.description||r.$t("sidebar.noDescription"),40)),1)]),e("div",It,a(X(new Date(E.updateTime||E.createTime))),1),e("div",Dt,[e("button",{class:"delete-task-btn",title:r.$t("sidebar.deleteTemplate"),onClick:re(Q=>o(f).deleteTemplate(E),["stop"])},[k(o($),{icon:"carbon:close",width:"16"})],8,xt)])],10,St))),128))])])):o(f).currentTab==="config"?(h(),g("div",Rt,[o(f).selectedTemplate?(h(),g("div",At,[e("div",Mt,[e("div",Nt,[e("h3",null,a(o(f).selectedTemplate.title||r.$t("sidebar.unnamedPlan")),1),e("span",Ut,"ID: "+a(o(f).selectedTemplate.id),1)]),e("button",{class:"back-to-list-btn",onClick:C[4]||(C[4]=E=>o(f).switchToTab("list"))},[k(o($),{icon:"carbon:arrow-left",width:"16"})])]),e("div",Lt,[e("div",Vt,[k(o($),{icon:"carbon:generate",width:"16"}),e("span",null,a(r.$t("sidebar.planGenerator")),1)]),e("div",qt,[pe(e("textarea",{"onUpdate:modelValue":C[5]||(C[5]=E=>o(f).generatorPrompt=E),class:"prompt-input",placeholder:r.$t("sidebar.generatorPlaceholder"),rows:"3"},null,8,Ft),[[be,o(f).generatorPrompt]]),e("div",Ot,[e("button",{class:"btn btn-primary btn-sm",onClick:T,disabled:o(f).isGenerating||!o(f).generatorPrompt.trim()},[k(o($),{icon:o(f).isGenerating?"carbon:circle-dash":"carbon:generate",width:"14",class:ne({spinning:o(f).isGenerating})},null,8,["icon","class"]),Z(" "+a(o(f).isGenerating?r.$t("sidebar.generating"):r.$t("sidebar.generatePlan")),1)],8,Bt),e("button",{class:"btn btn-secondary btn-sm",onClick:I,disabled:o(f).isGenerating||!o(f).generatorPrompt.trim()||!o(f).jsonContent.trim()},[k(o($),{icon:"carbon:edit",width:"14"}),Z(" "+a(r.$t("sidebar.updatePlan")),1)],8,Wt)])])]),e("div",jt,[e("div",Ht,[k(o($),{icon:"carbon:code",width:"16"}),e("span",null,a(r.$t("sidebar.jsonTemplate")),1),e("div",Jt,[e("button",{class:"btn btn-sm",onClick:C[6]||(C[6]=(...E)=>o(f).rollbackVersion&&o(f).rollbackVersion(...E)),disabled:!o(f).canRollback,title:r.$t("sidebar.rollback")},[k(o($),{icon:"carbon:undo",width:"14"})],8,zt),e("button",{class:"btn btn-sm",onClick:C[7]||(C[7]=(...E)=>o(f).restoreVersion&&o(f).restoreVersion(...E)),disabled:!o(f).canRestore,title:r.$t("sidebar.restore")},[k(o($),{icon:"carbon:redo",width:"14"})],8,Gt),e("button",{class:"btn btn-primary btn-sm",onClick:w,disabled:o(f).isGenerating||o(f).isExecuting},[k(o($),{icon:"carbon:save",width:"14"})],8,Xt)])]),pe(e("textarea",{"onUpdate:modelValue":C[8]||(C[8]=E=>m.value=E),class:"json-editor",placeholder:r.$t("sidebar.jsonPlaceholder"),rows:"12"},null,8,Kt),[[be,m.value]])]),e("div",Qt,[e("div",Yt,[k(o($),{icon:"carbon:play",width:"16"}),e("span",null,a(r.$t("sidebar.executionController")),1)]),e("div",Zt,[e("div",en,[e("label",null,a(r.$t("sidebar.executionParams")),1),e("div",tn,a(r.$t("sidebar.executionParamsHelp")),1),e("div",nn,[pe(e("input",{"onUpdate:modelValue":C[9]||(C[9]=E=>o(f).executionParams=E),class:"params-input",placeholder:r.$t("sidebar.executionParamsPlaceholder")},null,8,sn),[[be,o(f).executionParams]]),e("button",{class:"clear-params-btn",onClick:C[10]||(C[10]=(...E)=>o(f).clearExecutionParams&&o(f).clearExecutionParams(...E)),title:r.$t("sidebar.clearParams")},[k(o($),{icon:"carbon:close",width:"12"})],8,on)])]),e("div",an,[e("span",ln,a(r.$t("sidebar.apiUrl"))+":",1),e("code",cn,a(o(f).computedApiUrl),1)]),e("div",rn,[e("span",un,a(r.$t("sidebar.statusApiUrl"))+":",1),C[12]||(C[12]=e("code",{class:"api-url"},"/api/executor/details/{planId}",-1))]),e("button",{class:"btn btn-primary execute-btn",onClick:Y,disabled:o(f).isExecuting||o(f).isGenerating},[k(o($),{icon:o(f).isExecuting?"carbon:circle-dash":"carbon:play",width:"16",class:ne({spinning:o(f).isExecuting})},null,8,["icon","class"]),Z(" "+a(o(f).isExecuting?r.$t("sidebar.executing"):r.$t("sidebar.executePlan")),1)],8,dn)])])])):B("",!0)])):B("",!0)])],2))}}),hn=Se(pn,[["__scopeId","data-v-f6e5726e"]]);class Ue{static async sendMessage(l){const s=await fetch(`${this.BASE_URL}/execute`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:l})});if(!s.ok)throw new Error(`API request failed: ${s.status}`);return await s.json()}}ge(Ue,"BASE_URL","/api/executor");class Le{static async getDetails(l){try{const s=await fetch(`${this.BASE_URL}/details/${l}`);if(s.status===404)return null;if(!s.ok)throw new Error(`Failed to get detailed information: ${s.status}`);const u=await s.text(),L=JSON.parse(u);return L&&typeof L=="object"&&!L.currentPlanId&&(L.currentPlanId=l),L}catch(s){return console.error("[CommonApiService] Failed to get plan details:",s),null}}static async submitFormInput(l,s){const u=await fetch(`${this.BASE_URL}/submit-input/${l}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(!u.ok){let m;try{m=await u.json()}catch{m={message:`Failed to submit form input: ${u.status}`}}throw new Error(m.message||`Failed to submit form input: ${u.status}`)}const L=u.headers.get("content-type");return L&&L.indexOf("application/json")!==-1?await u.json():{success:!0}}static async getAllPrompts(){try{const l=await fetch(this.BASE_URL);return await(await this.handleResponse(l)).json()}catch(l){throw console.error("Failed to get Prompt list:",l),l}}static async handleResponse(l){if(!l.ok)try{const s=await l.json();throw new Error(s.message||`API request failed: ${l.status}`)}catch{throw new Error(`API request failed: ${l.status} ${l.statusText}`)}return l}}ge(Le,"BASE_URL","/api/executor");const $e=class $e{constructor(){ge(this,"POLL_INTERVAL",5e3);ge(this,"state",st({activePlanId:null,lastSequenceSize:0,isPolling:!1,pollTimer:null}));ge(this,"callbacks",{});ge(this,"planExecutionCache",new Map);ge(this,"uiStateCache",new Map);console.log("[PlanExecutionManager] Initialized with callback-based event system")}getCachedPlanRecord(l){return this.planExecutionCache.get(l)}getCachedUIState(l){return this.uiStateCache.get(l)}setCachedUIState(l,s){this.uiStateCache.set(l,s),console.log(`[PlanExecutionManager] Cached UI state for rootPlanId: ${l}`)}getAllCachedRecords(){return new Map(this.planExecutionCache)}hasCachedPlanRecord(l){return this.planExecutionCache.has(l)}setCachedPlanRecord(l,s){this.planExecutionCache.set(l,s),console.log(`[PlanExecutionManager] Cached plan execution record for rootPlanId: ${l}`)}clearCachedPlanRecord(l){const s=this.planExecutionCache.delete(l);return s&&console.log(`[PlanExecutionManager] Cleared cached plan execution record for rootPlanId: ${l}`),s}clearAllCachedRecords(){const l=this.planExecutionCache.size,s=this.uiStateCache.size;this.planExecutionCache.clear(),this.uiStateCache.clear(),console.log(`[PlanExecutionManager] Cleared all caches - Plans: ${l}, UI States: ${s}`)}static getInstance(){return $e.instance||($e.instance=new $e),$e.instance}getActivePlanId(){return this.state.activePlanId}getState(){return this.state}setEventCallbacks(l){this.callbacks={...this.callbacks,...l},console.log("[PlanExecutionManager] Event callbacks set:",Object.keys(l))}async handleUserMessageSendRequested(l){if(this.validateAndPrepareUIForNewRequest(l))try{if(await this.sendUserMessageAndSetPlanId(l),this.state.activePlanId)this.initiatePlanExecutionSequence(l,this.state.activePlanId);else throw new Error("Failed to get valid plan ID")}catch(s){console.error("[PlanExecutionManager] Failed to send user message:",s);const u=this.state.activePlanId??"error";this.setCachedUIState(u,{enabled:!0}),this.emitChatInputUpdateState(u),this.state.activePlanId=null}}handlePlanExecutionRequested(l,s){console.log("[PlanExecutionManager] Received plan execution request:",{planId:l,query:s}),l?(this.state.activePlanId=l,this.initiatePlanExecutionSequence(s??"执行计划",l)):console.error("[PlanExecutionManager] Invalid plan execution request: missing planId")}handleCachedPlanExecution(l,s){const u=this.getCachedPlanRecord(l);return u!=null&&u.currentPlanId?(console.log(`[PlanExecutionManager] Found cached plan execution record for rootPlanId: ${l}`),this.handlePlanExecutionRequested(u.currentPlanId,s),!0):(console.log(`[PlanExecutionManager] No cached plan execution record found for rootPlanId: ${l}`),!1)}validateAndPrepareUIForNewRequest(l){if(!l)return console.warn("[PlanExecutionManager] Query is empty"),!1;if(this.state.activePlanId)return!1;this.emitChatInputClear();const s=this.state.activePlanId??"ui-state";return this.setCachedUIState(s,{enabled:!1,placeholder:"Processing..."}),this.emitChatInputUpdateState(s),!0}async sendUserMessageAndSetPlanId(l){try{const s=await Ue.sendMessage(l);if(s!=null&&s.planId)return this.state.activePlanId=s.planId,s;if(s!=null&&s.planTemplateId)return this.state.activePlanId=s.planTemplateId,{...s,planId:s.planTemplateId};throw console.error("[PlanExecutionManager] Failed to get planId from response:",s),new Error("Failed to get valid planId from API response")}catch(s){throw console.error("[PlanExecutionManager] API call failed:",s),s}}initiatePlanExecutionSequence(l,s){console.log(`[PlanExecutionManager] Starting plan execution sequence for query: "${l}", planId: ${s}`);const u=s;this.emitDialogRoundStart(u),this.startPolling()}handlePlanCompletion(l){this.emitPlanCompleted(l.rootPlanId??""),this.state.lastSequenceSize=0,this.stopPolling();try{setTimeout(async()=>{if(this.state.activePlanId)try{await xe.deletePlanTemplate(this.state.activePlanId),console.log(`[PlanExecutionManager] Plan template ${this.state.activePlanId} deleted successfully`)}catch(s){console.log(`Delete plan execution record failed: ${s.message}`)}},5e3)}catch(s){console.log(`Delete plan execution record failed: ${s.message}`)}l.completed&&(this.state.activePlanId=null,this.emitChatInputUpdateState(l.rootPlanId??""))}async pollPlanStatus(){if(this.state.activePlanId){if(this.state.isPolling){console.log("[PlanExecutionManager] Previous polling still in progress, skipping");return}try{this.state.isPolling=!0;const l=await this.getPlanDetails(this.state.activePlanId);if(!l){console.warn("[PlanExecutionManager] No details received from API");return}if(l.rootPlanId&&this.setCachedPlanRecord(l.rootPlanId,l),!l.steps||l.steps.length===0){console.log("[PlanExecutionManager] Simple response without steps detected, handling as completed"),this.emitPlanUpdate(l.rootPlanId??""),this.handlePlanCompletion(l);return}this.emitPlanUpdate(l.rootPlanId??""),l.completed&&this.handlePlanCompletion(l)}catch(l){console.error("[PlanExecutionManager] Failed to poll plan status:",l)}finally{this.state.isPolling=!1}}}async getPlanDetails(l){try{const s=await Le.getDetails(l);return s!=null&&s.rootPlanId&&(this.planExecutionCache.set(s.rootPlanId,s),console.log(`[PlanExecutionManager] Cached plan execution record for rootPlanId: ${s.rootPlanId}`)),s}catch(s){return console.error("[PlanExecutionManager] Failed to get plan details:",s),null}}startPolling(){this.state.pollTimer&&clearInterval(this.state.pollTimer),this.state.pollTimer=window.setInterval(()=>{this.pollPlanStatus()},this.POLL_INTERVAL),console.log("[PlanExecutionManager] Started polling")}async pollPlanStatusImmediately(){console.log("[PlanExecutionManager] Polling plan status immediately"),await this.pollPlanStatus()}stopPolling(){this.state.pollTimer&&(clearInterval(this.state.pollTimer),this.state.pollTimer=null),console.log("[PlanExecutionManager] Stopped polling")}cleanup(){this.stopPolling(),this.state.activePlanId=null,this.state.lastSequenceSize=0,this.state.isPolling=!1,this.clearAllCachedRecords()}emitChatInputClear(){this.callbacks.onChatInputClear&&this.callbacks.onChatInputClear()}emitChatInputUpdateState(l){this.callbacks.onChatInputUpdateState&&this.callbacks.onChatInputUpdateState(l)}emitDialogRoundStart(l){this.callbacks.onDialogRoundStart&&this.callbacks.onDialogRoundStart(l)}emitPlanUpdate(l){this.callbacks.onPlanUpdate&&this.callbacks.onPlanUpdate(l)}emitPlanCompleted(l){this.callbacks.onPlanCompleted&&this.callbacks.onPlanCompleted(l)}};ge($e,"instance",null);let Ne=$e;const ae=Ne.getInstance(),gn={class:"right-panel"},mn={class:"preview-header"},vn={class:"preview-tabs"},fn={class:"tab-button active"},bn={class:"preview-content"},kn={class:"step-details"},_n={key:0,class:"step-info-fixed"},$n={key:0,class:"agent-info"},Pn={class:"info-item"},Cn={class:"label"},Sn={class:"value"},yn={class:"info-item"},En={class:"label"},wn={class:"value"},Tn={class:"info-item"},In={class:"label"},Dn={class:"value"},xn={class:"info-item"},Rn={class:"label"},An={class:"value"},Mn={class:"info-item"},Nn={class:"label"},Un={class:"execution-status"},Ln={class:"status-item"},Vn={class:"status-text"},qn={key:0},Fn={key:0,class:"think-act-steps"},On={class:"steps-container"},Bn={class:"step-header"},Wn={class:"step-number"},jn={class:"think-section"},Hn={class:"think-content"},Jn={class:"input"},zn={class:"label"},Gn={class:"output"},Xn={class:"label"},Kn={key:0,class:"action-section"},Qn={class:"action-content"},Yn={class:"tool-info"},Zn={class:"label"},es={class:"value"},ts={class:"input"},ns={class:"label"},ss={class:"output"},os={class:"label"},as={key:0,class:"sub-plan-section"},ls={class:"sub-plan-content"},is={class:"sub-plan-header"},cs={class:"sub-plan-info"},rs={class:"value"},us={key:0,class:"sub-plan-info"},ds={class:"value"},ps={class:"sub-plan-status"},hs={class:"status-text"},gs={key:0,class:"no-steps-message"},ms={key:1,class:"no-execution-message"},vs={class:"step-basic-info"},fs={class:"info-item"},bs={class:"label"},ks={class:"value"},_s={key:0,class:"info-item"},$s={class:"value"},Ps={class:"info-item"},Cs={class:"no-execution-hint"},Ss={key:2,class:"execution-indicator"},ys={class:"execution-text"},Es={key:1,class:"no-selection"},ws=["title"],Ts=Pe({__name:"index",setup(G,{expose:l}){const{t:s}=Ee(),u=x(),L=x(),m=x(),v=x(null),w=x(!1),T=x(!0),I=x(!0),Y=ke(()=>m.value?m.value.completed?s("rightPanel.status.completed"):m.value.current?s("rightPanel.status.executing"):s("rightPanel.status.waiting"):""),X=S=>{var F;if(console.log(`[RightPanel] updateDisplayedPlanProgress called with rootPlanId: ${S}`),m.value&&v.value){const M=v.value.rootPlanId??L.value;if(M&&M!==S){console.log(`[RightPanel] Plan ID mismatch - skipping update. Current: ${M}, Requested: ${S}`);return}}console.log(`[RightPanel] Plan ID validation passed - proceeding with update for rootPlanId: ${S}`);const P=ae.getCachedPlanRecord(S);if(!P){console.warn(`[RightPanel] Plan data not found for rootPlanId: ${S}`);return}if(P.steps&&P.steps.length>0){const M=P.steps.length,y=(P.currentStepIndex??0)+1;console.log(`[RightPanel] Progress: ${y} / ${M}`)}if(m.value&&L.value&&(L.value===S||((F=v.value)==null?void 0:F.rootPlanId)===S)&&(console.log(`[RightPanel] Refreshing selected step details for plan: ${S}`),v.value)){const y=v.value,N=r(y.planId,y.rootPlanId,y.subPlanId);N?(C(N,y.stepIndex,y.planId,y.isSubPlan),D()):console.warn("[RightPanel] Could not find plan record for refresh:",y)}},q=(S,P,F,M,y)=>{console.log("[RightPanel] Step selected:",{planId:S,stepIndex:P,rootPlanId:F,subPlanId:M,subStepIndex:y});const N=!!(F&&M&&y!==void 0);v.value={planId:S,stepIndex:P,isSubPlan:N,...N&&{rootPlanId:F,subPlanId:M,subStepIndex:y}};const te=r(S,F,M);if(!te){console.warn("[RightPanel] Plan data not found:",{planId:S,rootPlanId:F,subPlanId:M}),m.value=null,v.value=null;return}C(te,P,S,N)},r=(S,P,F)=>{var N;if(!P||!F)return ae.getCachedPlanRecord(S)??null;const M=ae.getCachedPlanRecord(S);if(M)return M;const y=ae.getCachedPlanRecord(P);if(!(y!=null&&y.agentExecutionSequence))return null;for(const te of y.agentExecutionSequence)if(te.thinkActSteps){for(const oe of te.thinkActSteps)if(((N=oe.subPlanExecutionRecord)==null?void 0:N.currentPlanId)===F)return oe.subPlanExecutionRecord}return null},C=(S,P,F,M)=>{var ue,b,A,c,_;if(!S.steps||P>=S.steps.length){m.value=null,v.value=null,console.warn("[RightPanel] Invalid step data:",{planId:F,stepIndex:P,hasSteps:!!S.steps,stepsLength:(ue=S.steps)==null?void 0:ue.length,message:"Invalid step index"});return}L.value=F;const y=S.steps[P],N=(b=S.agentExecutionSequence)==null?void 0:b[P];console.log("[RightPanel] Step data details:",{planId:F,stepIndex:P,step:y,hasAgentExecutionSequence:!!S.agentExecutionSequence,agentExecutionSequenceLength:(A=S.agentExecutionSequence)==null?void 0:A.length,agentExecution:N,hasThinkActSteps:!!(N!=null&&N.thinkActSteps),thinkActStepsLength:(c=N==null?void 0:N.thinkActSteps)==null?void 0:c.length,isSubPlan:M});const te=(N==null?void 0:N.status)==="FINISHED",oe=!te&&P===S.currentStepIndex&&!S.completed,ve={planId:F,index:P,title:typeof y=="string"?y:y.title||y.description||y.name||`${M?"子":""}步骤 ${P+1}`,description:typeof y=="string"?y:y.description||y,completed:te,current:oe};N&&(ve.agentExecution=N),m.value=ve,console.log("[RightPanel] Step details updated:",{planId:F,stepIndex:P,stepTitle:m.value.title,hasAgentExecution:!!N,hasThinkActSteps:(((_=N==null?void 0:N.thinkActSteps)==null?void 0:_.length)??0)>0,completed:te,current:oe,planCurrentStep:S.currentStepIndex,planCompleted:S.completed,isSubPlan:M}),N!=null&&N.thinkActSteps&&N.thinkActSteps.forEach((t,n)=>{t.subPlanExecutionRecord&&console.log(`[RightPanel] Found sub-plan in thinkActStep ${n}:`,t.subPlanExecutionRecord)}),setTimeout(()=>{p()},100),D()},E=(S,P,F,M)=>{console.log("[RightPanel] Sub plan step selected (delegating to unified handler):",{rootPlanId:S,subPlanId:P,stepIndex:F,subStepIndex:M}),q(P,M,S,P,M)},Q=S=>{u.value=S??void 0},p=()=>{if(!u.value)return;const{scrollTop:S,scrollHeight:P,clientHeight:F}=u.value,M=P-S-F<50,y=P>F;T.value=M,w.value=y&&!M,M?I.value=!0:P-S-F>100&&(I.value=!1),console.log("[RightPanel] Scroll state check:",{scrollTop:S,scrollHeight:P,clientHeight:F,isAtBottom:M,hasScrollableContent:y,showButton:w.value,shouldAutoScroll:I.value})},R=()=>{u.value&&(u.value.scrollTo({top:u.value.scrollHeight,behavior:"smooth"}),se(()=>{I.value=!0,p()}))},D=()=>{!I.value||!u.value||se(()=>{u.value&&(u.value.scrollTop=u.value.scrollHeight,console.log("[RightPanel] Auto scroll to bottom"))})},O=S=>{if(S===null||typeof S>"u"||S==="")return"N/A";try{const P=typeof S=="object"?S:JSON.parse(S);return JSON.stringify(P,null,2)}catch{return String(S)}},le=()=>{m.value=null,L.value=void 0,I.value=!0,u.value&&u.value.removeEventListener("scroll",p)},K=()=>{const S=()=>{const P=u.value;return P?(Q(P),P.addEventListener("scroll",p),I.value=!0,p(),console.log("[RightPanel] Scroll listener initialized successfully"),!0):(console.log("[RightPanel] Scroll container not found, retrying..."),!1)};se(()=>{S()||setTimeout(()=>{S()},100)})};return Ce(()=>{console.log("[RightPanel] Component mounted"),se(()=>{K()})}),we(()=>{console.log("[RightPanel] Component unmounting, cleaning up..."),v.value=null,le()}),l({updateDisplayedPlanProgress:X,handleStepSelected:q,handleSubPlanStepSelected:E}),(S,P)=>{var F,M;return h(),g("div",gn,[e("div",mn,[e("div",vn,[e("button",fn,[k(o($),{icon:"carbon:events"}),Z(" "+a(o(s)("rightPanel.stepExecutionDetails")),1)])])]),e("div",bn,[e("div",kn,[m.value?(h(),g("div",_n,[e("h3",null,a(m.value.title||m.value.description||o(s)("rightPanel.defaultStepTitle",{number:m.value.index+1})),1),m.value.agentExecution?(h(),g("div",$n,[e("div",Pn,[e("span",Cn,a(o(s)("rightPanel.executingAgent"))+":",1),e("span",Sn,a(m.value.agentExecution.agentName),1)]),e("div",yn,[e("span",En,a(o(s)("rightPanel.description"))+":",1),e("span",wn,a(m.value.agentExecution.agentDescription||""),1)]),e("div",Tn,[e("span",In,a(o(s)("rightPanel.callingModel"))+":",1),e("span",Dn,a(m.value.agentExecution.modelName),1)]),e("div",xn,[e("span",Rn,a(o(s)("rightPanel.request"))+":",1),e("span",An,a(m.value.agentExecution.agentRequest||""),1)]),e("div",Mn,[e("span",Nn,a(o(s)("rightPanel.executionResult"))+":",1),e("span",{class:ne(["value",{success:m.value.agentExecution.status==="FINISHED"}])},a(m.value.agentExecution.status||o(s)("rightPanel.executing")),3)])])):B("",!0),e("div",Un,[e("div",Ln,[m.value.completed?(h(),de(o($),{key:0,icon:"carbon:checkmark-filled",class:"status-icon success"})):m.value.current?(h(),de(o($),{key:1,icon:"carbon:in-progress",class:"status-icon progress"})):(h(),de(o($),{key:2,icon:"carbon:time",class:"status-icon pending"})),e("span",Vn,a(Y.value),1)])])])):B("",!0),e("div",{ref_key:"scrollContainer",ref:u,class:"step-details-scroll-container",onScroll:p},[m.value?(h(),g("div",qn,[(F=m.value.agentExecution)!=null&&F.thinkActSteps&&m.value.agentExecution.thinkActSteps.length>0?(h(),g("div",Fn,[e("h4",null,a(o(s)("rightPanel.thinkAndActionSteps")),1),e("div",On,[(h(!0),g(me,null,fe(m.value.agentExecution.thinkActSteps,(y,N)=>(h(),g("div",{key:N,class:"think-act-step"},[e("div",Bn,[e("span",Wn,"#"+a(N+1),1),e("span",{class:ne(["step-status",y.status])},a(y.status||o(s)("rightPanel.executing")),3)]),e("div",jn,[e("h5",null,[k(o($),{icon:"carbon:thinking"}),Z(" "+a(o(s)("rightPanel.thinking")),1)]),e("div",Hn,[e("div",Jn,[e("span",zn,a(o(s)("rightPanel.input"))+":",1),e("pre",null,a(O(y.thinkInput)),1)]),e("div",Gn,[e("span",Xn,a(o(s)("rightPanel.output"))+":",1),e("pre",null,a(O(y.thinkOutput)),1)])])]),y.actionNeeded?(h(),g("div",Kn,[e("h5",null,[k(o($),{icon:"carbon:play"}),Z(" "+a(o(s)("rightPanel.action")),1)]),e("div",Qn,[(h(!0),g(me,null,fe(y.actToolInfoList,(te,oe)=>(h(),g("div",{key:oe},[e("div",Yn,[e("span",Zn,a(o(s)("rightPanel.tool"))+":",1),e("span",es,a(te.name||""),1)]),e("div",ts,[e("span",ns,a(o(s)("rightPanel.toolParameters"))+":",1),e("pre",null,a(O(te.parameters)),1)]),e("div",ss,[e("span",os,a(o(s)("rightPanel.executionResult"))+":",1),e("pre",null,a(O(te.result)),1)])]))),128))]),y.subPlanExecutionRecord?(h(),g("div",as,[e("h5",null,[k(o($),{icon:"carbon:tree-view"}),Z(" "+a(o(s)("rightPanel.subPlan")),1)]),e("div",ls,[e("div",is,[e("div",cs,[P[0]||(P[0]=e("span",{class:"label"},"子计划ID:",-1)),e("span",rs,a(y.subPlanExecutionRecord.currentPlanId),1)]),y.subPlanExecutionRecord.title?(h(),g("div",us,[P[1]||(P[1]=e("span",{class:"label"},"标题:",-1)),e("span",ds,a(y.subPlanExecutionRecord.title),1)])):B("",!0),e("div",ps,[y.subPlanExecutionRecord.completed?(h(),de(o($),{key:0,icon:"carbon:checkmark-filled",class:"status-icon success"})):(h(),de(o($),{key:1,icon:"carbon:in-progress",class:"status-icon progress"})),e("span",hs,a(y.subPlanExecutionRecord.completed?"已完成":"执行中"),1)])])])])):B("",!0)])):B("",!0)]))),128))]),m.value.agentExecution&&!((M=m.value.agentExecution.thinkActSteps)!=null&&M.length)?(h(),g("div",gs,[e("p",null,a(o(s)("rightPanel.noStepDetails")),1)])):m.value.agentExecution?B("",!0):(h(),g("div",ms,[k(o($),{icon:"carbon:information",class:"info-icon"}),e("h4",null,a(o(s)("rightPanel.stepInfo")),1),e("div",vs,[e("div",fs,[e("span",bs,a(o(s)("rightPanel.stepName"))+":",1),e("span",ks,a(m.value.title||m.value.description||`步骤 ${m.value.index+1}`),1)]),m.value.description?(h(),g("div",_s,[P[2]||(P[2]=e("span",{class:"label"},"描述:",-1)),e("span",$s,a(m.value.description),1)])):B("",!0),e("div",Ps,[P[3]||(P[3]=e("span",{class:"label"},"状态:",-1)),e("span",{class:ne(["value",{"status-completed":m.value.completed,"status-current":m.value.current,"status-pending":!m.value.completed&&!m.value.current}])},a(m.value.completed?"已完成":m.value.current?"执行中":"待执行"),3)])]),e("p",Cs,a(o(s)("rightPanel.noExecutionInfo")),1)])),m.value.current&&!m.value.completed?(h(),g("div",Ss,[P[4]||(P[4]=e("div",{class:"execution-waves"},[e("div",{class:"wave wave-1"}),e("div",{class:"wave wave-2"}),e("div",{class:"wave wave-3"})],-1)),e("p",ys,[k(o($),{icon:"carbon:in-progress",class:"rotating-icon"}),Z(" "+a(o(s)("rightPanel.stepExecuting")),1)])])):B("",!0)])):(h(),g("div",Es,[k(o($),{icon:"carbon:events",class:"empty-icon"}),e("h3",null,a(o(s)("rightPanel.noStepSelected")),1),e("p",null,a(o(s)("rightPanel.selectStepHint")),1)]))])):B("",!0),k(Ie,{name:"scroll-button"},{default:De(()=>[w.value?(h(),g("button",{key:0,onClick:R,class:"scroll-to-bottom-btn",title:o(s)("rightPanel.scrollToBottom")},[k(o($),{icon:"carbon:chevron-down"})],8,ws)):B("",!0)]),_:1})],544)])])])}}}),Is=Se(Ts,[["__scopeId","data-v-e90596ce"]]);function Ds(){const G=ae,l=ke(()=>G.getActivePlanId()),s=ke(()=>G.getState()),u=ke(()=>s.value.isPolling),L=ke(()=>!!l.value),m=(I,Y)=>{G.initiatePlanExecutionSequence(I,Y)},v=()=>{G.stopPolling()},w=()=>{G.startPolling()},T=()=>{G.cleanup()};return we(()=>{T()}),{activePlanId:l,state:s,isPolling:u,hasActivePlan:L,startExecution:m,stopPolling:v,startPolling:w,cleanup:T}}const xs={class:"chat-container"},Rs={class:"message-content"},As={key:0,class:"user-message"},Ms={key:1,class:"assistant-message"},Ns={key:0,class:"thinking-section"},Us={class:"thinking-header"},Ls={class:"thinking-avatar"},Vs={class:"thinking-label"},qs={class:"thinking-content"},Fs={key:0,class:"thinking"},Os={key:1,class:"progress"},Bs={class:"progress-bar"},Ws={class:"progress-text"},js={key:2,class:"steps-container"},Hs={class:"steps-title"},Js=["onClick"],zs={class:"section-header"},Gs={class:"step-icon"},Xs={class:"step-title"},Ks={key:0,class:"step-status current"},Qs={key:1,class:"step-status completed"},Ys={key:2,class:"step-status pending"},Zs={key:0,class:"action-info"},eo={class:"action-description"},to={class:"action-icon"},no={key:0,class:"tool-params"},so={class:"param-label"},oo={class:"param-content"},ao={key:1,class:"think-details"},lo={class:"think-header"},io={class:"think-label"},co={class:"think-output"},ro={class:"think-content"},uo={key:1,class:"sub-plan-steps"},po={class:"sub-plan-header"},ho={class:"sub-plan-step-list"},go=["onClick"],mo={class:"sub-step-indicator"},vo={class:"sub-step-icon"},fo={class:"sub-step-number"},bo={class:"sub-step-content"},ko={class:"sub-step-title"},_o={key:2,class:"user-input-form-container"},$o={class:"user-input-message"},Po={key:0,class:"form-description"},Co=["onSubmit"],So=["for"],yo=["id","name","onUpdate:modelValue"],Eo={key:1,class:"form-group"},wo={for:"form-input-genericInput"},To=["onUpdate:modelValue"],Io={type:"submit",class:"submit-user-input-btn"},Do={key:3,class:"default-processing"},xo={class:"processing-indicator"},Ro={class:"response-section"},Ao={class:"response-header"},Mo={class:"response-avatar"},No={class:"response-name"},Uo={class:"response-content"},Lo={key:0,class:"final-response"},Vo=["innerHTML"],qo={key:1,class:"response-placeholder"},Fo={class:"typing-indicator"},Oo={class:"typing-text"},Bo={key:0,class:"message assistant"},Wo={class:"message-content"},jo={class:"assistant-message"},Ho={class:"thinking-section"},Jo={class:"thinking-header"},zo={class:"thinking-avatar"},Go={class:"thinking-label"},Xo={class:"thinking-content"},Ko={class:"default-processing"},Qo={class:"processing-indicator"},Yo={class:"response-section"},Zo={class:"response-header"},ea={class:"response-avatar"},ta={class:"response-name"},na={class:"response-content"},sa={class:"response-placeholder"},oa={class:"typing-indicator"},aa={class:"typing-text"},la=["title"],ia=Pe({__name:"index",props:{mode:{default:"plan"},initialPrompt:{default:""}},emits:["step-selected","sub-plan-step-selected"],setup(G,{expose:l,emit:s}){const u=G,L=s,{t:m}=Ee(),v=Ds(),w=x(),T=x(!1),I=x([]),Y=x(),X=x(!1),q=st({}),r=(t,n,i)=>{const d={id:Date.now().toString(),type:t,content:n,timestamp:new Date,...i};return t==="assistant"&&!d.thinking&&!d.content&&(d.thinking=m("chat.thinking")),I.value.push(d),d},C=t=>{const n=I.value[I.value.length-1];n.type==="assistant"&&Object.assign(n,t)},E=async t=>{try{T.value=!0;const n=r("assistant","",{thinking:"正在理解您的请求并准备回复..."}),i=await Ue.sendMessage(t);if(i.planId)console.log("[ChatComponent] Received planId from direct execution:",i.planId),n.planExecution||(n.planExecution={}),n.planExecution.currentPlanId=i.planId,ae.handlePlanExecutionRequested(i.planId,t),delete n.thinking,console.log("[ChatComponent] Started polling for plan execution updates");else{delete n.thinking;const d=Q(i,t);n.content=d}}catch(n){console.error("Direct mode error:",n),C({content:p(n)})}finally{T.value=!1}},Q=(t,n)=>t.result??t.message??t.content??"",p=t=>{const n=(t==null?void 0:t.message)??(t==null?void 0:t.toString())??"未知错误";return n.includes("网络")||n.includes("network")||n.includes("timeout")?"抱歉，似乎网络连接有些问题。请检查您的网络连接后再试一次，或者稍等几分钟再重新提问。":n.includes("认证")||n.includes("权限")||n.includes("auth")?"抱歉，访问权限出现了问题。这可能是系统配置的问题，请联系管理员或稍后再试。":n.includes("格式")||n.includes("参数")||n.includes("invalid")?"抱歉，您的请求格式可能有些问题。能否请您重新表述一下您的需求？我会尽力理解并帮助您。":`抱歉，处理您的请求时遇到了一些问题（${n}）。请稍后再试，或者换个方式表达您的需求，我会尽力帮助您的。`},R=(t=!1)=>{se(()=>{if(w.value){const n=w.value;(t||n.scrollHeight-n.scrollTop-n.clientHeight<150)&&n.scrollTo({top:n.scrollHeight,behavior:t?"auto":"smooth"})}})},D=()=>{R(!0),X.value=!1},O=()=>{if(w.value){const t=w.value,n=t.scrollHeight-t.scrollTop-t.clientHeight<150;X.value=!n&&I.value.length>0}},le=()=>{w.value&&w.value.addEventListener("scroll",O)},K=()=>{w.value&&w.value.removeEventListener("scroll",O)},S=t=>{r("user",t),u.mode==="plan"?console.log("[ChatComponent] Plan mode message sent, parent should handle:",t):E(t)},P=(t,n)=>{var W;const i=((W=t.planExecution)==null?void 0:W.agentExecutionSequence)??[];return n<0||n>=i.length?"IDLE":i[n].status??"IDLE"},F=(t,n)=>{var i,d;if(!((i=t.planExecution)!=null&&i.currentPlanId)){console.warn("[ChatComponent] Cannot handle step click: missing currentPlanId");return}console.log("[ChatComponent] Step clicked:",{planId:t.planExecution.currentPlanId,stepIndex:n,stepTitle:(d=t.planExecution.steps)==null?void 0:d[n]}),L("step-selected",t.planExecution.currentPlanId,n)},M=(t,n)=>{var i;try{const d=(i=t.planExecution)==null?void 0:i.agentExecutionSequence;if(!(d!=null&&d.length))return console.log("[ChatComponent] No agentExecutionSequence found"),[];const W=d[n];if(!W)return console.log(`[ChatComponent] No agentExecution found for step ${n}`),[];if(!W.thinkActSteps)return console.log(`[ChatComponent] No thinkActSteps found for step ${n}`),[];for(const V of W.thinkActSteps)if(V.subPlanExecutionRecord)return console.log(`[ChatComponent] Found sub-plan for step ${n}:`,V.subPlanExecutionRecord),(V.subPlanExecutionRecord.steps??[]).map(j=>typeof j=="string"?j:typeof j=="object"&&j!==null&&(j.title||j.description)||"子步骤");return[]}catch(d){return console.warn("[ChatComponent] Error getting sub-plan steps:",d),[]}},y=(t,n,i)=>{var d;try{const W=(d=t.planExecution)==null?void 0:d.agentExecutionSequence;if(!(W!=null&&W.length))return"pending";const V=W[n];if(!V||!V.thinkActSteps)return"pending";let ee=null;for(const H of V.thinkActSteps)if(H.subPlanExecutionRecord){ee=H.subPlanExecutionRecord;break}if(!ee)return"pending";const j=ee.currentStepIndex;return ee.completed?"completed":j==null?i===0?"current":"pending":i<j?"completed":i===j?"current":"pending"}catch(W){return console.warn("[ChatComponent] Error getting sub-plan step status:",W),"pending"}},N=(t,n,i)=>{var d,W;try{const V=(d=t.planExecution)==null?void 0:d.agentExecutionSequence;if(!(V!=null&&V.length)){console.warn("[ChatComponent] No agentExecutionSequence data for sub-plan step click");return}const ee=V[n];if(!ee){console.warn("[ChatComponent] No agentExecution found for step",n);return}if(!ee.thinkActSteps){console.warn("[ChatComponent] No thinkActSteps found for step",n);return}let j=null;for(const H of ee.thinkActSteps)if(H.subPlanExecutionRecord){j=H.subPlanExecutionRecord;break}if(!(j!=null&&j.currentPlanId)){console.warn("[ChatComponent] No sub-plan data for step click");return}L("sub-plan-step-selected",((W=t.planExecution)==null?void 0:W.currentPlanId)??"",j.currentPlanId,n,i)}catch(V){console.error("[ChatComponent] Error handling sub-plan step click:",V)}},te=(t,n)=>{var d,W,V,ee;if(!((d=t.planExecution)!=null&&d.steps))return;console.log("[ChatComponent] Starting to update step actions, steps count:",t.planExecution.steps.length,"execution sequence:",((W=n.agentExecutionSequence)==null?void 0:W.length)??0);const i=new Array(t.planExecution.steps.length).fill(null);if((V=n.agentExecutionSequence)!=null&&V.length){const j=Math.min(n.agentExecutionSequence.length,t.planExecution.steps.length);for(let H=0;H<j;H++){const U=n.agentExecutionSequence[H];if((ee=U.thinkActSteps)!=null&&ee.length){const J=U.thinkActSteps[U.thinkActSteps.length-1];J.actionDescription&&J.toolParameters?(i[H]={actionDescription:J.actionDescription,toolParameters:typeof J.toolParameters=="string"?J.toolParameters:JSON.stringify(J.toolParameters,null,2),thinkInput:J.thinkInput??"",thinkOutput:J.thinkOutput??"",status:n.currentStepIndex!==void 0&&H<n.currentStepIndex?"completed":n.currentStepIndex!==void 0&&H===n.currentStepIndex?"current":"pending"},console.log(`[ChatComponent] Step ${H} action set: ${i[H].actionDescription}`)):(i[H]={actionDescription:"思考中",toolParameters:"等待决策",thinkInput:J.thinkInput??"",thinkOutput:J.thinkOutput??"",status:n.currentStepIndex!==void 0&&H===n.currentStepIndex?"current":"pending"},console.log(`[ChatComponent] Step ${H} is thinking`))}else i[H]={actionDescription:n.currentStepIndex!==void 0&&H<n.currentStepIndex?"已完成":"等待中",toolParameters:"无工具参数",thinkInput:"",thinkOutput:"",status:n.currentStepIndex!==void 0&&H<n.currentStepIndex?"completed":"pending"},console.log(`[ChatComponent] 步骤 ${H} 无执行细节, 状态设为: ${i[H].status}`)}}else console.log("[ChatComponent] 没有执行序列数据");t.stepActions=[...i],console.log("[ChatComponent] 步骤动作更新完成:",JSON.stringify(i.map(j=>j==null?void 0:j.actionDescription))),se(()=>{console.log("[ChatComponent] UI update completed via reactivity")})},oe=t=>{console.log("[ChatComponent] Starting dialog round with planId:",t),t&&(I.value.findIndex(i=>{var d;return((d=i.planExecution)==null?void 0:d.currentPlanId)===t&&i.type==="assistant"})===-1?(r("assistant","",{planExecution:{currentPlanId:t},thinking:"正在准备执行计划..."}),console.log("[ChatComponent] Created new assistant message for planId:",t)):console.log("[ChatComponent] Found existing assistant message for planId:",t))},ve=t=>{var V,ee,j,H;console.log("[ChatComponent] Processing plan update with rootPlanId:",t);const n=ae.getCachedPlanRecord(t);if(!n){console.warn("[ChatComponent] No cached plan data found for rootPlanId:",t);return}if(console.log("[ChatComponent] Retrieved plan details from cache:",n),console.log("[ChatComponent] Plan steps:",n.steps),console.log("[ChatComponent] Plan completed:",n.completed),!n.currentPlanId){console.warn("[ChatComponent] Plan update missing currentPlanId");return}const i=I.value.findIndex(U=>{var J;return((J=U.planExecution)==null?void 0:J.currentPlanId)===n.currentPlanId&&U.type==="assistant"});let d;if(i!==-1)d=I.value[i],console.log("[ChatComponent] Found existing assistant message for currentPlanId:",n.currentPlanId);else{console.warn("[ChatComponent] No existing assistant message found for currentPlanId:",n.currentPlanId),console.log("[ChatComponent] Current messages:",I.value.map(J=>{var ie;return{type:J.type,planId:(ie=J.planExecution)==null?void 0:ie.currentPlanId,content:J.content.substring(0,50)}}));let U=-1;for(let J=I.value.length-1;J>=0;J--)if(I.value[J].type==="assistant"){U=J;break}if(U!==-1)d=I.value[U],d.planExecution||(d.planExecution={}),d.planExecution.currentPlanId=n.currentPlanId,console.log("[ChatComponent] Using last assistant message and updating planExecution.currentPlanId to:",n.currentPlanId);else{console.error("[ChatComponent] No assistant message found at all, this should not happen");return}}if(d.planExecution||(d.planExecution={}),d.planExecution=JSON.parse(JSON.stringify(n)),!n.steps||n.steps.length===0){if(console.log("[ChatComponent] Handling simple response without steps"),n.completed){delete d.thinking;const U=n.summary??n.result??n.message??"处理完成";d.content=ue(U),console.log("[ChatComponent] Set simple response content:",d.content)}else n.title&&(d.thinking=`正在执行: ${n.title}`);return}delete d.thinking;const W=n.steps.map(U=>typeof U=="string"?U:typeof U=="object"&&U!==null&&(U.title||U.description)||"步骤");if(d.planExecution&&(d.planExecution.steps=W),n.agentExecutionSequence&&n.agentExecutionSequence.length>0){console.log("[ChatComponent] 发现执行序列数据，数量:",n.agentExecutionSequence.length),te(d,n);const U=n.currentStepIndex??0;if(U>=0&&U<n.agentExecutionSequence.length){const ie=n.agentExecutionSequence[U].thinkActSteps;if(ie&&ie.length>0){const ye=ie[ie.length-1];if(ye.thinkOutput){const Re=ye.thinkOutput.length>150?ye.thinkOutput.substring(0,150)+"...":ye.thinkOutput;d.thinking=`正在思考: ${Re}`}}}}else if(d.planExecution){const U=d.planExecution.currentStepIndex??0,J=(V=d.planExecution.steps)==null?void 0:V[U],ie=typeof J=="string"?J:"";d.thinking=`正在执行: ${ie}`}if(n.userInputWaitState&&d.planExecution?(console.log("[ChatComponent] 需要用户输入:",n.userInputWaitState),d.planExecution.userInputWaitState||(d.planExecution.userInputWaitState={}),d.planExecution.userInputWaitState={message:n.userInputWaitState.message??"",formDescription:n.userInputWaitState.formDescription??"",formInputs:((ee=n.userInputWaitState.formInputs)==null?void 0:ee.map(U=>({label:U.label,value:U.value||""})))??[]},q[j=d.id]??(q[j]={}),d.thinking="等待用户输入..."):(H=d.planExecution)!=null&&H.userInputWaitState&&delete d.planExecution.userInputWaitState,n.completed??n.status==="completed"){console.log("[ChatComponent] Plan is completed, updating final response"),delete d.thinking;let U="";n.summary?U=n.summary:n.result?U=n.result:U="任务已完成",d.content=b(U),console.log("[ChatComponent] Updated completed message:",d.content)}se(()=>{console.log("[ChatComponent] Plan update UI refresh completed via reactivity")})},ue=t=>t?t.includes("我")||t.includes("您")||t.includes("您好")||t.includes("可以")?t:t.length<10?`${t}！还有什么需要我帮助的吗？`:t.length<50?`好的，${t}。如果您还有其他问题，请随时告诉我。`:`${t}

希望这个回答对您有帮助！还有什么我可以为您做的吗？`:"我明白了，还有什么我可以帮您的吗？",b=t=>t?`${t}`:"任务已完成！还有什么我可以帮您的吗？",A=t=>{console.log("[ChatComponent] Plan completed with rootPlanId:",t);const n=ae.getCachedPlanRecord(t);if(!n){console.warn("[ChatComponent] No cached plan data found for rootPlanId:",t);return}if(console.log("[ChatComponent] Plan details:",n),n.rootPlanId){const i=I.value.findIndex(d=>{var W;return((W=d.planExecution)==null?void 0:W.currentPlanId)===n.rootPlanId});if(i!==-1){const d=I.value[i];delete d.thinking;let V=n.summary??n.result??"任务已完成";!V.includes("我")&&!V.includes("您")&&(V.includes("成功")||V.includes("完成")?V=`很好！${V}。如果您还有其他需要帮助的地方，请随时告诉我。`:V=`我已经完成了您的请求：${V}`),d.content=V,console.log("[ChatComponent] Updated completed message:",d.content)}else console.warn("[ChatComponent] No message found for completed rootPlanId:",n.rootPlanId)}},c=t=>{if(!t)return"";let n=t.replace(/\n\n/g,"<br><br>").replace(/\n/g,"<br>");return n=n.replace(/(<br><br>)/g,"</p><p>"),n.includes("</p><p>")&&(n=`<p>${n}</p>`),n},_=async t=>{var n;if(!((n=t.planExecution)!=null&&n.currentPlanId)||!t.planExecution.userInputWaitState){console.error("[ChatComponent] 缺少planExecution.currentPlanId或userInputWaitState");return}try{const i={},d=t.planExecution.userInputWaitState.formInputs;d&&d.length>0?Object.entries(q[t.id]).forEach(([V,ee])=>{var U;const j=parseInt(V,10),H=((U=d[j])==null?void 0:U.label)||`input_${V}`;i[H]=ee}):i.genericInput=t.genericInput??"",console.log("[ChatComponent] 提交用户输入:",i);const W=await Le.submitFormInput(t.planExecution.currentPlanId,i);delete t.planExecution.userInputWaitState,delete t.genericInput,delete q[t.id],v.startPolling(),console.log("[ChatComponent] 用户输入提交成功:",W)}catch(i){console.error("[ChatComponent] 用户输入提交失败:",i),alert(`提交失败: ${(i==null?void 0:i.message)||"未知错误"}`)}};return _e(()=>u.initialPrompt,(t,n)=>{console.log("[ChatComponent] initialPrompt changed from:",n,"to:",t),t&&typeof t=="string"&&t.trim()&&t!==n&&(console.log("[ChatComponent] Processing changed initial prompt:",t),se(()=>{S(t)}))},{immediate:!1}),Ce(()=>{console.log("[ChatComponent] Mounted, setting up event listeners"),ae.setEventCallbacks({onPlanUpdate:ve,onPlanCompleted:A,onDialogRoundStart:oe,onChatInputUpdateState:t=>{console.log("[ChatComponent] Chat input state update for rootPlanId:",t)},onChatInputClear:()=>{console.log("[ChatComponent] Chat input clear requested")}}),se(()=>{le()}),u.initialPrompt&&typeof u.initialPrompt=="string"&&u.initialPrompt.trim()&&(console.log("[ChatComponent] Processing initial prompt:",u.initialPrompt),se(()=>{S(u.initialPrompt)}))}),we(()=>{console.log("[ChatComponent] Unmounting, cleaning up resources"),K(),Y.value&&clearInterval(Y.value),v.cleanup(),Object.keys(q).forEach(t=>delete q[t])}),l({handleSendMessage:S,handlePlanUpdate:ve,handlePlanCompleted:A,handleDialogRoundStart:oe,addMessage:r}),(t,n)=>(h(),g("div",xs,[e("div",{class:"messages",ref_key:"messagesRef",ref:w},[(h(!0),g(me,null,fe(I.value,i=>{var d,W,V,ee,j,H,U,J,ie;return h(),g("div",{key:i.id,class:ne(["message",{user:i.type==="user",assistant:i.type==="assistant"}])},[e("div",Rs,[i.type==="user"?(h(),g("div",As,a(i.content),1)):(h(),g("div",Ms,[i.thinking||((d=i.planExecution)==null?void 0:d.progress)!==void 0||(((V=(W=i.planExecution)==null?void 0:W.steps)==null?void 0:V.length)??0)>0?(h(),g("div",Ns,[e("div",Us,[e("div",Ls,[k(o($),{icon:"carbon:thinking",class:"thinking-icon"})]),e("div",Vs,a(t.$t("chat.thinkingLabel")),1)]),e("div",qs,[i.thinking?(h(),g("div",Fs,[k(o($),{icon:"carbon:thinking",class:"thinking-icon"}),e("span",null,a(i.thinking),1)])):B("",!0),((ee=i.planExecution)==null?void 0:ee.progress)!==void 0?(h(),g("div",Os,[e("div",Bs,[e("div",{class:"progress-fill",style:Me({width:i.planExecution.progress+"%"})},null,4)]),e("span",Ws,a(i.planExecution.progressText??t.$t("chat.processing")+"..."),1)])):B("",!0),(((H=(j=i.planExecution)==null?void 0:j.steps)==null?void 0:H.length)??0)>0?(h(),g("div",js,[e("h4",Hs,a(t.$t("chat.stepExecutionDetails")),1),(h(!0),g(me,null,fe((U=i.planExecution)==null?void 0:U.steps,(ye,z)=>{var Re,Ve,qe,Fe,Oe,Be,We,je,He,Je,ze,Ge,Xe,Ke,Qe,Ye,Ze,et,tt;return h(),g("div",{key:z,class:ne(["ai-section",{running:P(i,z)==="RUNNING",completed:P(i,z)==="FINISHED",pending:P(i,z)==="IDLE"}]),onClick:re(he=>F(i,z),["stop"])},[e("div",zs,[e("span",Gs,a(P(i,z)==="FINISHED"?"✓":P(i,z)==="RUNNING"?"▶":"○"),1),e("span",Xs,a(ye||`${t.$t("chat.step")} ${z+1}`),1),P(i,z)==="RUNNING"?(h(),g("span",Ks,a(t.$t("chat.status.executing")),1)):P(i,z)==="FINISHED"?(h(),g("span",Qs,a(t.$t("chat.status.completed")),1)):(h(),g("span",Ys,a(t.$t("chat.status.pending")),1))]),i.stepActions&&i.stepActions[z]?(h(),g("div",Zs,[e("div",eo,[e("span",to,a(((Re=i.stepActions[z])==null?void 0:Re.status)==="current"?"🔄":((Ve=i.stepActions[z])==null?void 0:Ve.status)==="completed"?"✓":"⏳"),1),e("strong",null,a((qe=i.stepActions[z])==null?void 0:qe.actionDescription),1)]),(Fe=i.stepActions[z])!=null&&Fe.toolParameters?(h(),g("div",no,[n[0]||(n[0]=e("span",{class:"tool-icon"},"⚙️",-1)),e("span",so,a(t.$t("common.parameters"))+":",1),e("pre",oo,a((Oe=i.stepActions[z])==null?void 0:Oe.toolParameters),1)])):B("",!0),(Be=i.stepActions[z])!=null&&Be.thinkOutput?(h(),g("div",ao,[e("div",lo,[n[1]||(n[1]=e("span",{class:"think-icon"},"💭",-1)),e("span",io,a(t.$t("chat.thinkingOutput"))+":",1)]),e("div",co,[e("pre",ro,a((We=i.stepActions[z])==null?void 0:We.thinkOutput),1)])])):B("",!0)])):B("",!0),((je=M(i,z))==null?void 0:je.length)>0?(h(),g("div",uo,[e("div",po,[k(o($),{icon:"carbon:tree-view",class:"sub-plan-icon"}),n[2]||(n[2]=e("span",{class:"sub-plan-title"},"子执行计划",-1))]),e("div",ho,[(h(!0),g(me,null,fe(M(i,z),(he,ce)=>(h(),g("div",{key:`sub-${z}-${ce}`,class:ne(["sub-plan-step-item",{completed:y(i,z,ce)==="completed",current:y(i,z,ce)==="current",pending:y(i,z,ce)==="pending"}]),onClick:re(nt=>N(i,z,ce),["stop"])},[e("div",mo,[e("span",vo,a(y(i,z,ce)==="completed"?"✓":y(i,z,ce)==="current"?"▶":"○"),1),e("span",fo,a(ce+1),1)]),e("div",bo,[e("span",ko,a(he),1),n[3]||(n[3]=e("span",{class:"sub-step-badge"},"子步骤",-1))])],10,go))),128))])])):B("",!0),(He=i.planExecution)!=null&&He.userInputWaitState&&P(i,z)==="RUNNING"?(h(),g("div",_o,[e("p",$o,a(((ze=(Je=i.planExecution)==null?void 0:Je.userInputWaitState)==null?void 0:ze.message)??t.$t("chat.userInput.message")),1),(Xe=(Ge=i.planExecution)==null?void 0:Ge.userInputWaitState)!=null&&Xe.formDescription?(h(),g("p",Po,a((Qe=(Ke=i.planExecution)==null?void 0:Ke.userInputWaitState)==null?void 0:Qe.formDescription),1)):B("",!0),e("form",{onSubmit:re(he=>_(i),["prevent"]),class:"user-input-form"},[(Ze=(Ye=i.planExecution)==null?void 0:Ye.userInputWaitState)!=null&&Ze.formInputs&&i.planExecution.userInputWaitState.formInputs.length>0?(h(!0),g(me,{key:0},fe((tt=(et=i.planExecution)==null?void 0:et.userInputWaitState)==null?void 0:tt.formInputs,(he,ce)=>(h(),g("div",{key:ce,class:"form-group"},[e("label",{for:`form-input-${he.label.replace(/\W+/g,"_")}`},a(he.label)+": ",9,So),pe(e("input",{type:"text",id:`form-input-${he.label.replace(/\W+/g,"_")}`,name:he.label,"onUpdate:modelValue":nt=>q[i.id][ce]=nt,class:"form-input"},null,8,yo),[[be,q[i.id][ce]]])]))),128)):(h(),g("div",Eo,[e("label",wo,a(t.$t("common.input"))+":",1),pe(e("input",{type:"text",id:"form-input-genericInput",name:"genericInput","onUpdate:modelValue":he=>i.genericInput=he,class:"form-input"},null,8,To),[[be,i.genericInput]])])),e("button",Io,a(t.$t("chat.userInput.submit")),1)],40,Co)])):B("",!0)],10,Js)}),128))])):!i.content&&(i.thinking||((J=i.planExecution)==null?void 0:J.progress)!==void 0&&(((ie=i.planExecution)==null?void 0:ie.progress)??0)<100)?(h(),g("div",Do,[e("div",xo,[n[4]||(n[4]=e("div",{class:"thinking-dots"},[e("span"),e("span"),e("span")],-1)),e("span",null,a(i.thinking??t.$t("chat.thinkingProcessing")),1)])])):B("",!0)])])):B("",!0),e("div",Ro,[e("div",Ao,[e("div",Mo,[k(o($),{icon:"carbon:bot",class:"bot-icon"})]),e("div",No,a(t.$t("chat.botName")),1)]),e("div",Uo,[i.content?(h(),g("div",Lo,[e("div",{class:"response-text",innerHTML:c(i.content)},null,8,Vo)])):(h(),g("div",qo,[e("div",Fo,[n[5]||(n[5]=e("div",{class:"typing-dots"},[e("span"),e("span"),e("span")],-1)),e("span",Oo,a(t.$t("chat.thinkingResponse")),1)])]))])])]))])],2)}),128)),T.value?(h(),g("div",Bo,[e("div",Wo,[e("div",jo,[e("div",Ho,[e("div",Jo,[e("div",zo,[k(o($),{icon:"carbon:thinking",class:"thinking-icon"})]),e("div",Go,a(t.$t("chat.thinkingLabel")),1)]),e("div",Xo,[e("div",Ko,[e("div",Qo,[n[6]||(n[6]=e("div",{class:"thinking-dots"},[e("span"),e("span"),e("span")],-1)),e("span",null,a(t.$t("chat.thinking")),1)])])])]),e("div",Yo,[e("div",Zo,[e("div",ea,[k(o($),{icon:"carbon:bot",class:"bot-icon"})]),e("div",ta,a(t.$t("chat.botName")),1)]),e("div",na,[e("div",sa,[e("div",oa,[n[7]||(n[7]=e("div",{class:"typing-dots"},[e("span"),e("span"),e("span")],-1)),e("span",aa,a(t.$t("chat.thinkingResponse")),1)])])])])])])])):B("",!0)],512),X.value?(h(),g("div",{key:0,class:"scroll-to-bottom-btn",onClick:D,title:t.$t("chat.scrollToBottom")},[k(o($),{icon:"carbon:chevron-down"})],8,la)):B("",!0)]))}}),ca=Se(ia,[["__scopeId","data-v-d1fb4f30"]]),ra={class:"input-area"},ua={class:"input-container"},da={class:"attach-btn",title:"附加文件"},pa=["placeholder","disabled"],ha=["title"],ga=["disabled","title"],ma=Pe({__name:"index",props:{placeholder:{default:""},disabled:{type:Boolean,default:!1},initialValue:{default:""}},emits:["send","clear","update-state","plan-mode-clicked"],setup(G,{expose:l,emit:s}){const{t:u}=Ee(),L=G,m=s,v=x(),w=x(""),T=ke(()=>L.placeholder||u("input.placeholder")),I=x(T.value),Y=ke(()=>!!L.disabled),X=()=>{se(()=>{v.value&&(v.value.style.height="auto",v.value.style.height=Math.min(v.value.scrollHeight,120)+"px")})},q=D=>{D.key==="Enter"&&!D.shiftKey&&(D.preventDefault(),r())},r=()=>{if(!w.value.trim()||Y.value)return;const D=w.value.trim();m("send",D),E()},C=()=>{m("plan-mode-clicked")},E=()=>{w.value="",X(),m("clear")},Q=(D,O)=>{O&&(I.value=D?O:u("input.waiting")),m("update-state",D,O)},p=D=>{w.value=D,X()},R=()=>w.value.trim();return _e(()=>L.initialValue,D=>{D&&D.trim()&&(w.value=D,X())},{immediate:!0}),l({clearInput:E,updateState:Q,setInputValue:p,getQuery:R,focus:()=>{var D;return(D=v.value)==null?void 0:D.focus()}}),Ce(()=>{}),we(()=>{}),(D,O)=>(h(),g("div",ra,[e("div",ua,[e("button",da,[k(o($),{icon:"carbon:attachment"})]),pe(e("textarea",{"onUpdate:modelValue":O[0]||(O[0]=le=>w.value=le),ref_key:"inputRef",ref:v,class:"chat-input",placeholder:I.value,disabled:Y.value,onKeydown:q,onInput:X},null,40,pa),[[be,w.value]]),e("button",{class:"plan-mode-btn",title:D.$t("input.planMode"),onClick:C},[k(o($),{icon:"carbon:document"}),Z(" "+a(D.$t("input.planMode")),1)],8,ha),e("button",{class:"send-button",disabled:!w.value.trim()||Y.value,onClick:r,title:D.$t("input.send")},[k(o($),{icon:"carbon:send-alt"}),Z(" "+a(D.$t("input.send")),1)],8,ga)])]))}}),va=Se(ma,[["__scopeId","data-v-639c8b2a"]]);class Te{static async getAllCronTasks(){try{const l=await fetch(this.BASE_URL);return await(await this.handleResponse(l)).json()}catch(l){throw console.error("Failed to get cron tasks:",l),l}}static async getCronTaskById(l){try{const s=await fetch(`${this.BASE_URL}/${l}`);return await(await this.handleResponse(s)).json()}catch(s){throw console.error("Failed to get cron task by id:",s),s}}static async createCronTask(l){try{const s=await fetch(this.BASE_URL,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(l)});return await(await this.handleResponse(s)).json()}catch(s){throw console.error("Failed to create cron task:",s),s}}static async updateCronTask(l,s){try{const u=await fetch(`${this.BASE_URL}/${l}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});return await(await this.handleResponse(u)).json()}catch(u){throw console.error("Failed to update cron task:",u),u}}static async updateTaskStatus(l,s){try{const u=await fetch(`${this.BASE_URL}/${l}/status?status=${s}`,{method:"PUT"});await this.handleResponse(u)}catch(u){throw console.error("Failed to update task status:",u),u}}static async deleteCronTask(l){try{const s=await fetch(`${this.BASE_URL}/${l}`,{method:"DELETE"});await this.handleResponse(s)}catch(s){throw console.error("Failed to delete cron task:",s),s}}static async handleResponse(l){if(!l.ok)try{const s=await l.json();throw new Error(s.message||`API request failed: ${l.status}`)}catch{throw new Error(`API request failed: ${l.status} ${l.statusText}`)}return l}}ge(Te,"BASE_URL","/api/cron-tasks");const fa={class:"modal-header"},ba={class:"header-actions"},ka={class:"status-switch"},_a={class:"status-label"},$a={class:"toggle-switch"},Pa=["checked"],Ca={class:"modal-content"},Sa={class:"form-group"},ya={class:"form-label"},Ea=["placeholder"],wa={class:"form-group"},Ta={class:"form-label"},Ia=["placeholder"],Da={class:"form-help"},xa={class:"form-group"},Ra={class:"form-label"},Aa=["placeholder"],Ma={class:"form-group"},Na={class:"form-label"},Ua={class:"template-toggle"},La={key:0,class:"template-selector"},Va={value:""},qa=["value"],Fa={class:"form-help"},Oa={key:0,class:"form-group"},Ba={class:"time-info"},Wa={class:"time-label"},ja={class:"time-value"},Ha={key:1,class:"form-group"},Ja={class:"time-info"},za={class:"time-label"},Ga={class:"time-value"},Xa={class:"modal-footer"},Ka=["disabled"],Qa=Pe({__name:"TaskDetailModal",props:{modelValue:{type:Boolean},task:{}},emits:["update:modelValue","save"],setup(G,{emit:l}){const s=G,u=l,L=x(!1),m=x([]),v=x({cronName:"",cronTime:"",planDesc:"",status:1,linkTemplate:!1,templateId:"",planTemplateId:""}),w=x({}),T=async()=>{try{const p=await xe.getAllPlanTemplates();p&&p.templates&&(m.value=p.templates.map(R=>({id:R.id,name:R.title||"未命名模板"})))}catch(p){console.error("获取模板列表失败:",p)}};Ce(()=>{T()});const I=p=>{p.target===p.currentTarget&&u("update:modelValue",!1)},Y=()=>(w.value={},v.value.cronName.trim()||(w.value.cronName="任务名称不能为空"),v.value.cronTime.trim()?X(v.value.cronTime)||(w.value.cronTime="Cron表达式格式不正确"):w.value.cronTime="Cron表达式不能为空",v.value.planDesc.trim()||(w.value.planDesc="任务描述不能为空"),v.value.linkTemplate&&!v.value.templateId?(w.value.templateId="请选择一个计划模板",console.log("验证错误：模板ID为空")):v.value.linkTemplate&&console.log("验证通过：选择了模板ID",v.value.templateId),Object.keys(w.value).length===0),X=p=>p.trim().split(/\s+/).length===6,q=async()=>{var p,R;if(!Y()){const D=Object.values(w.value)[0];D&&alert(D);return}L.value=!0;try{const D={...v.value,...((p=s.task)==null?void 0:p.id)!==void 0&&{id:s.task.id},cronName:v.value.cronName.trim(),cronTime:v.value.cronTime.trim(),planDesc:v.value.planDesc.trim()||"",status:v.value.status,linkTemplate:v.value.linkTemplate||!1,planTemplateId:v.value.linkTemplate&&v.value.templateId||""};if(console.log("保存任务，templateId:",D.templateId,"关联模板:",D.linkTemplate),(R=s.task)!=null&&R.id)console.log("更新任务:",D),u("save",D);else try{console.log("创建新任务:",D);const O=await xe.createCronTask(D);console.log("创建任务响应:",O),u("save",O)}catch(O){console.error("创建定时任务失败:",O),alert(O.message||"创建定时任务失败")}}finally{L.value=!1}},r=p=>new Date(p).toLocaleString(),C=()=>{console.log("模板选择变更:",v.value.templateId),v.value.templateId&&(v.value.linkTemplate=!0)},E=()=>{v.value.linkTemplate=!0,console.log("启用模板关联")},Q=()=>{v.value.linkTemplate=!1,v.value.templateId="",console.log("禁用模板关联，清空模板ID")};return _e(()=>s.task,p=>{p?v.value={cronName:p.cronName||"",cronTime:p.cronTime||"",planDesc:p.planDesc||"",status:p.status||1,linkTemplate:!!(p.templateId||p.planTemplateId),templateId:p.templateId||p.planTemplateId||"",planTemplateId:p.planTemplateId||p.templateId||""}:v.value={cronName:"",cronTime:"",planDesc:"",status:1,linkTemplate:!1,templateId:"",planTemplateId:""}},{immediate:!0}),_e(()=>s.modelValue,p=>{p||(v.value={cronName:"",cronTime:"",planDesc:"",status:1,linkTemplate:!1,templateId:"",planTemplateId:""})}),(p,R)=>(h(),de(Ae,{to:"body"},[k(Ie,{name:"modal"},{default:De(()=>{var D,O,le;return[p.modelValue?(h(),g("div",{key:0,class:"modal-overlay",onClick:I},[e("div",{class:"modal-container",onClick:R[7]||(R[7]=re(()=>{},["stop"]))},[e("div",fa,[e("h3",null,a(p.$t("cronTask.taskDetail")),1),e("div",ba,[e("div",ka,[e("span",_a,a(p.$t("cronTask.taskStatus")),1),e("label",$a,[e("input",{type:"checkbox",checked:v.value.status===0,onChange:R[0]||(R[0]=K=>v.value.status=v.value.status===0?1:0)},null,40,Pa),R[8]||(R[8]=e("span",{class:"toggle-slider"},null,-1))])]),e("button",{class:"close-btn",onClick:R[1]||(R[1]=K=>p.$emit("update:modelValue",!1))},[k(o($),{icon:"carbon:close"})])])]),e("div",Ca,[e("form",{onSubmit:re(q,["prevent"]),class:"task-form"},[e("div",Sa,[e("label",ya,a(p.$t("cronTask.taskName")),1),pe(e("input",{"onUpdate:modelValue":R[2]||(R[2]=K=>v.value.cronName=K),type:"text",class:"form-input",placeholder:p.$t("cronTask.taskNamePlaceholder"),required:""},null,8,Ea),[[be,v.value.cronName]])]),e("div",wa,[e("label",Ta,a(p.$t("cronTask.cronExpression")),1),pe(e("input",{"onUpdate:modelValue":R[3]||(R[3]=K=>v.value.cronTime=K),type:"text",class:"form-input",placeholder:p.$t("cronTask.cronExpressionPlaceholder"),required:""},null,8,Ia),[[be,v.value.cronTime]]),e("div",Da,a(p.$t("cronTask.cronExpressionHelp")),1)]),e("div",xa,[e("label",Ra,a(p.$t("cronTask.taskDescription")),1),pe(e("textarea",{"onUpdate:modelValue":R[4]||(R[4]=K=>v.value.planDesc=K),class:"form-textarea",placeholder:p.$t("cronTask.taskDescriptionPlaceholder"),rows:"4",required:""},null,8,Aa),[[be,v.value.planDesc]])]),e("div",Ma,[e("label",Na,a(p.$t("cronTask.planTemplate")),1),e("div",Ua,[e("button",{type:"button",class:ne(["template-btn",v.value.linkTemplate?"active":""]),onClick:E},[k(o($),{icon:"carbon:checkmark"}),Z(" "+a(p.$t("cronTask.linkTemplate")),1)],2),e("button",{type:"button",class:ne(["template-btn",v.value.linkTemplate?"":"active"]),onClick:Q},[k(o($),{icon:"carbon:close"}),Z(" "+a(p.$t("cronTask.noTemplate")),1)],2)]),v.value.linkTemplate?(h(),g("div",La,[pe(e("select",{"onUpdate:modelValue":R[5]||(R[5]=K=>v.value.templateId=K),class:"form-select",onChange:C},[e("option",Va,a(p.$t("cronTask.selectTemplate")),1),(h(!0),g(me,null,fe(m.value,K=>(h(),g("option",{key:K.id,value:K.id},a(K.name),9,qa))),128))],544),[[ct,v.value.templateId]]),e("div",Fa,a(p.$t("cronTask.templateHelpText")),1)])):B("",!0)]),(D=p.task)!=null&&D.createTime?(h(),g("div",Oa,[e("div",Ba,[e("span",Wa,a(p.$t("cronTask.createTime"))+":",1),e("span",ja,a(r(p.task.createTime)),1)])])):B("",!0),(O=p.task)!=null&&O.updateTime?(h(),g("div",Ha,[e("div",Ja,[e("span",za,a(p.$t("cronTask.updateTime"))+":",1),e("span",Ga,a(r(p.task.updateTime)),1)])])):B("",!0)],32)]),e("div",Xa,[e("button",{type:"button",class:"cancel-btn",onClick:R[6]||(R[6]=K=>p.$emit("update:modelValue",!1))},a(p.$t("common.cancel")),1),e("button",{type:"button",class:"save-btn",onClick:q,disabled:L.value},[L.value?(h(),de(o($),{key:0,icon:"carbon:loading",class:"loading-icon"})):B("",!0),Z(" "+a((le=s.task)!=null&&le.id?p.$t("common.save"):p.$t("common.create")),1)],8,Ka)])])])):B("",!0)]}),_:1})]))}}),Ya=Se(Qa,[["__scopeId","data-v-ec045854"]]),Za={class:"modal-header"},el={class:"header-actions"},tl={class:"modal-content"},nl={key:0,class:"loading-container"},sl={key:1,class:"empty-container"},ol={key:2,class:"task-list"},al=["onClick"],ll={class:"task-main"},il={class:"task-info"},cl={class:"task-header"},rl={class:"task-name"},ul={class:"task-description"},dl={class:"task-time"},pl=["onClick"],hl=["onClick","disabled","title"],gl=["onClick","title"],ml={class:"dropdown-menu"},vl=["onClick"],fl=["onClick","disabled"],bl=["onClick","disabled"],kl={class:"confirm-header"},_l={class:"confirm-content"},$l={class:"confirm-actions"},Pl=["disabled"],Cl={class:"confirm-header"},Sl={class:"confirm-content"},yl={class:"create-options"},El={class:"option-content"},wl={class:"option-title"},Tl={class:"option-desc"},Il={class:"option-content"},Dl={class:"option-title"},xl={class:"option-desc"},Rl={class:"confirm-actions"},Al=Pe({__name:"index",props:{modelValue:{type:Boolean,required:!0}},emits:["update:modelValue"],setup(G,{emit:l}){const s=ot(),u=at(),L=pt(),{t:m}=Ee(),v=G,w=l,T=x([]),I=x(!1),Y=x(null),X=x(null),q=x(null),r=x(null),C=x(!1),E=x(null),Q=x(!1),p=x(null),R=x(!1),D=c=>{c.target===c.currentTarget&&w("update:modelValue",!1)},O=async()=>{I.value=!0;try{T.value=await Te.getAllCronTasks()}catch(c){console.error("Failed to load cron tasks:",c)}finally{I.value=!1}},le=async c=>{Y.value=c;try{const _=T.value.find(n=>n.id===c);if(!_){console.error("Task not found:",c);return}w("update:modelValue",!1);const t=Date.now().toString();if(await s.push({name:"direct",params:{id:t}}),await new Promise(n=>setTimeout(n,100)),_.planTemplateId){console.log("[CronTaskModal] 使用模板执行计划:",_.planTemplateId);const n={title:_.cronName||"定时任务执行",planData:{id:_.planTemplateId,planTemplateId:_.planTemplateId,planId:_.planTemplateId},params:_.executionParams||void 0};u.emitPlanExecutionRequested(n)}else{console.log("[CronTaskModal] 使用原逻辑执行计划");const n=_.planDesc||_.cronName||"";n.trim()&&u.setTask(n.trim())}}catch(_){console.error("Failed to execute task:",_)}finally{Y.value=null}},K=c=>{E.value={...c},C.value=!0,r.value=null},S=async c=>{try{c.id&&(await Te.updateCronTask(Number(c.id),c),await O()),C.value=!1}catch(_){console.error("Failed to save task:",_)}},P=c=>{p.value=c,Q.value=!0},F=async()=>{var c;if((c=p.value)!=null&&c.id){X.value=p.value.id;try{await Te.deleteCronTask(String(p.value.id)),await O(),Q.value=!1,p.value=null}catch(_){console.error("Failed to delete task:",_)}finally{X.value=null}}},M=()=>{Q.value=!1,p.value=null},y=c=>{r.value=r.value===c?null:c},N=async c=>{if(c.id){q.value=c.id;try{const _=c.status===0?1:0;await Te.updateCronTask(Number(c.id),{...c,status:_}),await O(),r.value=null}catch(_){console.error("Failed to toggle task status:",_)}finally{q.value=null}}},te=async c=>{try{await navigator.clipboard.writeText(c),L.success("成功复制cron表达式")}catch(_){L.error(`复制失败: ${_ instanceof Error?_.message:String(_)}`)}},oe=()=>{R.value=!0},ve=()=>{console.log("[CronTaskModal] createWithJmanus called"),R.value=!1;try{w("update:modelValue",!1),console.log("[CronTaskModal] Modal closed");const c=m("cronTask.template");u.setTaskToInput(c),console.log("[CronTaskModal] Template set to input store:",c);const _=Date.now().toString();console.log("[CronTaskModal] Generated chatId:",_),s.push({name:"direct",params:{id:_}}).then(()=>{console.log("[CronTaskModal] Navigation successful")}).catch(t=>{console.error("[CronTaskModal] Navigation error:",t)})}catch(c){console.error("[CronTaskModal] Error in createWithJmanus:",c)}},ue=()=>{console.log("[CronTaskModal] createManually called"),R.value=!1,E.value={cronName:"",cronTime:"",planDesc:"",status:0,planTemplateId:""},C.value=!0},b=()=>{R.value=!1},A=c=>{const _=c.target;!_.closest(".action-dropdown")&&!_.closest(".dropdown-menu")&&(r.value=null)};return Ce(()=>{document.addEventListener("click",A,!0)}),we(()=>{document.removeEventListener("click",A,!0)}),_e(()=>v.modelValue,c=>{c&&O()}),(c,_)=>(h(),g(me,null,[(h(),de(Ae,{to:"body"},[k(Ie,{name:"modal"},{default:De(()=>[G.modelValue?(h(),g("div",{key:0,class:"modal-overlay",onClick:D},[e("div",{class:"modal-container",onClick:_[3]||(_[3]=re(()=>{},["stop"]))},[e("div",Za,[e("h3",null,a(c.$t("cronTask.title")),1),e("div",el,[e("button",{class:"add-task-btn",onClick:[oe,_[0]||(_[0]=re(()=>{},["stop"]))]},[k(o($),{icon:"carbon:add"}),Z(" "+a(c.$t("cronTask.addTask")),1)]),e("button",{class:"close-btn",onClick:_[1]||(_[1]=t=>c.$emit("update:modelValue",!1))},[k(o($),{icon:"carbon:close"})])])]),e("div",tl,[I.value?(h(),g("div",nl,[k(o($),{icon:"carbon:loading",class:"loading-icon"}),e("span",null,a(c.$t("common.loading")),1)])):T.value.length===0?(h(),g("div",sl,[k(o($),{icon:"carbon:time",class:"empty-icon"}),e("span",null,a(c.$t("cronTask.noTasks")),1)])):(h(),g("div",ol,[(h(!0),g(me,null,fe(T.value,t=>(h(),g("div",{key:t.id||"",class:"task-item",onClick:n=>K(t)},[e("div",ll,[e("div",il,[e("div",cl,[e("div",rl,a(t.cronName),1),e("div",{class:ne(["task-status-badge",t.status===0?"active":"inactive"])},[k(o($),{icon:t.status===0?"carbon:checkmark-filled":"carbon:pause-filled"},null,8,["icon"]),e("span",null,a(t.status===0?c.$t("cronTask.active"):c.$t("cronTask.inactive")),1)],2)]),e("div",ul,a(t.planDesc),1),e("div",dl,[k(o($),{icon:"carbon:time"}),e("span",{class:"cron-readable",style:{cursor:"pointer"},onClick:re(n=>te(t.cronTime),["stop"])},a(t.cronTime),9,pl)])])]),e("div",{class:"task-actions",onClick:_[2]||(_[2]=re(()=>{},["stop"]))},[e("button",{class:"action-btn execute-btn",onClick:n=>le(t.id),disabled:Y.value===t.id,title:c.$t("cronTask.executeOnce")},[k(o($),{icon:Y.value===t.id?"carbon:loading":"carbon:play-filled"},null,8,["icon"]),Z(" "+a(c.$t("cronTask.executeOnce")),1)],8,hl),e("div",{class:ne(["action-dropdown",{active:r.value===t.id}])},[e("button",{class:"action-btn dropdown-btn",onClick:n=>y(t.id),title:c.$t("cronTask.operations")},[k(o($),{icon:"carbon:overflow-menu-horizontal"}),Z(" "+a(c.$t("cronTask.operations")),1)],8,gl),pe(e("div",ml,[e("button",{class:"dropdown-item edit-btn",onClick:n=>K(t)},[k(o($),{icon:"carbon:edit"}),Z(" "+a(c.$t("cronTask.edit")),1)],8,vl),e("button",{class:"dropdown-item toggle-btn",onClick:n=>N(t),disabled:q.value===t.id},[k(o($),{icon:q.value===t.id?"carbon:loading":t.status===0?"carbon:pause-filled":"carbon:play-filled"},null,8,["icon"]),Z(" "+a(t.status===0?c.$t("cronTask.disable"):c.$t("cronTask.enable")),1)],8,fl),e("button",{class:"dropdown-item delete-btn",onClick:n=>P(t),disabled:X.value===t.id},[k(o($),{icon:X.value===t.id?"carbon:loading":"carbon:trash-can"},null,8,["icon"]),Z(" "+a(c.$t("cronTask.delete")),1)],8,bl)],512),[[rt,r.value===t.id]])],2)])],8,al))),128))]))])])])):B("",!0)]),_:1})])),k(Ya,{modelValue:C.value,"onUpdate:modelValue":_[4]||(_[4]=t=>C.value=t),task:E.value,onSave:S},null,8,["modelValue","task"]),(h(),de(Ae,{to:"body"},[k(Ie,{name:"modal"},{default:De(()=>{var t,n,i,d;return[Q.value?(h(),g("div",{key:0,class:"modal-overlay",onClick:M},[e("div",{class:"confirm-modal",onClick:_[5]||(_[5]=re(()=>{},["stop"]))},[e("div",kl,[k(o($),{icon:"carbon:warning",class:"warning-icon"}),e("h3",null,a(c.$t("cronTask.deleteConfirm")),1)]),e("div",_l,[e("p",null,a(c.$t("cronTask.deleteConfirmMessage",{taskName:((t=p.value)==null?void 0:t.cronName)||((n=p.value)==null?void 0:n.planDesc)||""})),1)]),e("div",$l,[e("button",{class:"confirm-btn cancel-btn",onClick:M},a(c.$t("common.cancel")),1),e("button",{class:"confirm-btn delete-btn",onClick:F,disabled:X.value===((i=p.value)==null?void 0:i.id)},[k(o($),{icon:X.value===((d=p.value)==null?void 0:d.id)?"carbon:loading":"carbon:trash-can"},null,8,["icon"]),Z(" "+a(c.$t("cronTask.delete")),1)],8,Pl)])])])):B("",!0)]}),_:1})])),(h(),de(Ae,{to:"body"},[k(Ie,{name:"modal"},{default:De(()=>[R.value?(h(),g("div",{key:0,class:"modal-overlay",onClick:b},[e("div",{class:"confirm-modal create-options-modal",onClick:_[6]||(_[6]=re(()=>{},["stop"]))},[e("div",Cl,[k(o($),{icon:"carbon:time",class:"create-icon"}),e("h3",null,a(c.$t("cronTask.createTask")),1)]),e("div",Sl,[e("p",null,a(c.$t("cronTask.selectCreateMethod")),1),e("div",yl,[e("button",{class:"create-option-btn jmanus-btn",onClick:ve},[k(o($),{icon:"carbon:ai-status"}),e("div",El,[e("span",wl,a(c.$t("cronTask.createWithJmanus")),1),e("span",Tl,a(c.$t("cronTask.createWithJmanusDesc")),1)])]),e("button",{class:"create-option-btn manual-btn",onClick:ue},[k(o($),{icon:"carbon:edit"}),e("div",Il,[e("span",Dl,a(c.$t("cronTask.createManually")),1),e("span",xl,a(c.$t("cronTask.createManuallyDesc")),1)])])])]),e("div",Rl,[e("button",{class:"confirm-btn cancel-btn",onClick:b},a(c.$t("common.cancel")),1)])])])):B("",!0)]),_:1})]))],64))}}),Ml=Se(Al,[["__scopeId","data-v-ed0b5f01"]]),Nl={class:"direct-page"},Ul={class:"direct-chat"},Ll={class:"chat-header"},Vl={class:"header-actions"},ql=["title"],Fl=["title"],Ol={class:"chat-content"},Bl=["title"],Wl=Pe({__name:"index",setup(G){const l=ut(),s=ot(),u=at(),{t:L}=Ee(),m=x(""),v=x(""),w=x(),T=x(),I=x(),Y=x(!1),X=x(!1),q=x(null),r=x(!1),C=x(50),E=x(!1),Q=x(0),p=x(0);Ce(()=>{if(console.log("[Direct] onMounted called"),console.log("[Direct] taskStore.currentTask:",u.currentTask),console.log("[Direct] taskStore.hasUnprocessedTask():",u.hasUnprocessedTask()),ae.setEventCallbacks({onPlanUpdate:c=>{console.log("[Direct] Plan update event received for rootPlanId:",c),K(c)&&(console.log("[Direct] Processing plan update for current rootPlanId:",c),T.value&&typeof T.value.handlePlanUpdate=="function"?(console.log("[Direct] Calling chatRef.handlePlanUpdate with rootPlanId:",c),T.value.handlePlanUpdate(c)):console.warn("[Direct] chatRef.handlePlanUpdate method not available"),w.value&&typeof w.value.updateDisplayedPlanProgress=="function"?(console.log("[Direct] Calling rightPanelRef.updateDisplayedPlanProgress with rootPlanId:",c),w.value.updateDisplayedPlanProgress(c)):console.warn("[Direct] rightPanelRef.updateDisplayedPlanProgress method not available"))},onPlanCompleted:c=>{if(console.log("[Direct] Plan completed event received for rootPlanId:",c),!!K(c)){if(console.log("[Direct] Processing plan completion for current rootPlanId:",c),T.value&&typeof T.value.handlePlanCompleted=="function"){const _=ae.getCachedPlanRecord(c);console.log("[Direct] Calling chatRef.handlePlanCompleted with details:",_),T.value.handlePlanCompleted(_??{planId:c})}else console.warn("[Direct] chatRef.handlePlanCompleted method not available");q.value=null,console.log("[Direct] Cleared currentRootPlanId after plan completion")}},onDialogRoundStart:c=>{console.log("[Direct] Dialog round start event received for rootPlanId:",c),q.value=c,console.log("[Direct] Set currentRootPlanId to:",c),T.value&&typeof T.value.handleDialogRoundStart=="function"?(console.log("[Direct] Calling chatRef.handleDialogRoundStart with planId:",c),T.value.handleDialogRoundStart(c)):console.warn("[Direct] chatRef.handleDialogRoundStart method not available")},onChatInputClear:()=>{console.log("[Direct] Chat input clear event received"),P()},onChatInputUpdateState:c=>{if(console.log("[Direct] Chat input update state event received for rootPlanId:",c),!K(c,!0))return;const _=ae.getCachedUIState(c);_&&M(_.enabled,_.placeholder)}}),console.log("[Direct] Event callbacks registered to planExecutionManager"),f.loadPlanTemplateList(),u.hasUnprocessedTask()&&u.currentTask){const c=u.currentTask.prompt;console.log("[Direct] Found unprocessed task from store:",c),u.markTaskAsProcessed(),se(()=>{T.value&&typeof T.value.handleSendMessage=="function"?(console.log("[Direct] Directly executing task via chatRef.handleSendMessage:",c),T.value.handleSendMessage(c)):(console.warn("[Direct] chatRef.handleSendMessage method not available, falling back to prompt"),m.value=c)})}else{const c=u.getAndClearTaskToInput();c?(v.value=c,console.log("[Direct] Setting inputOnlyContent for input only:",v.value)):(m.value=l.query.prompt||"",console.log("[Direct] Received task from URL:",m.value),console.log("[Direct] No unprocessed task in store"))}const b=localStorage.getItem("directPanelWidth");b&&(C.value=parseFloat(b)),console.log("[Direct] Final prompt value:",m.value),v.value&&se(()=>{I.value&&typeof I.value.setInputValue=="function"&&(I.value.setInputValue(v.value),console.log("[Direct] Set input value:",v.value),v.value="")}),window.addEventListener("plan-execution-requested",c=>{console.log("[DirectView] Received plan-execution-requested event:",c.detail),ue(c.detail)});const A=sessionStorage.getItem("pending-plan-execution");if(A){console.log("[DirectView] Found pending plan execution in sessionStorage");try{const c=JSON.parse(A);sessionStorage.removeItem("pending-plan-execution"),console.log("[DirectView] Executing pending plan:",c),ue(c)}catch(c){console.error("[DirectView] Failed to parse pending plan execution:",c),sessionStorage.removeItem("pending-plan-execution")}}}),_e(()=>u.currentTask,b=>{if(console.log("[Direct] Watch taskStore.currentTask triggered, newTask:",b),b&&!b.processed){const A=b.prompt;u.markTaskAsProcessed(),console.log("[Direct] Received new task from store:",A),se(()=>{T.value&&typeof T.value.handleSendMessage=="function"?(console.log("[Direct] Directly executing new task via chatRef.handleSendMessage:",A),T.value.handleSendMessage(A)):console.warn("[Direct] chatRef.handleSendMessage method not available for new task")})}else console.log("[Direct] Task is null or already processed, ignoring")},{immediate:!1}),_e(()=>m.value,(b,A)=>{console.log("[Direct] prompt value changed from:",A,"to:",b)},{immediate:!1}),_e(()=>u.taskToInput,b=>{console.log("[Direct] Watch taskStore.taskToInput triggered, newTaskToInput:",b),b&&b.trim()&&(console.log("[Direct] Setting input value from taskToInput:",b),se(()=>{I.value&&typeof I.value.setInputValue=="function"&&(I.value.setInputValue(b.trim()),console.log("[Direct] Input value set from taskToInput watch:",b.trim()),u.getAndClearTaskToInput())}))},{immediate:!1}),we(()=>{console.log("[Direct] onUnmounted called, cleaning up resources"),q.value=null,ae.cleanup(),document.removeEventListener("mousemove",D),document.removeEventListener("mouseup",O),window.removeEventListener("plan-execution-requested",b=>{ue(b.detail)})});const R=b=>{E.value=!0,Q.value=b.clientX,p.value=C.value,document.addEventListener("mousemove",D),document.addEventListener("mouseup",O),document.body.style.cursor="col-resize",document.body.style.userSelect="none",b.preventDefault()},D=b=>{if(!E.value)return;const A=window.innerWidth,_=(b.clientX-Q.value)/A*100;let t=p.value+_;t=Math.max(20,Math.min(80,t)),C.value=t},O=()=>{E.value=!1,document.removeEventListener("mousemove",D),document.removeEventListener("mouseup",O),document.body.style.cursor="",document.body.style.userSelect="",localStorage.setItem("directPanelWidth",C.value.toString())},le=()=>{C.value=50,localStorage.setItem("directPanelWidth","50")},K=(b,A=!1)=>!q.value||b===q.value||A&&(b==="ui-state"||b==="error")?!0:(console.log("[Direct] Ignoring event for non-current rootPlanId:",b,"current:",q.value),!1),S=b=>{console.log("[DirectView] Send message from input:",b),T.value&&typeof T.value.handleSendMessage=="function"?(console.log("[DirectView] Calling chatRef.handleSendMessage:",b),T.value.handleSendMessage(b)):console.warn("[DirectView] chatRef.handleSendMessage method not available")},P=()=>{console.log("[DirectView] Input cleared"),I.value&&typeof I.value.clear=="function"&&I.value.clear()},F=()=>{console.log("[DirectView] Input focused")},M=(b,A)=>{console.log("[DirectView] Input state updated:",b,A),X.value=!b},y=(b,A)=>{console.log("[DirectView] Step selected:",b,A),w.value&&typeof w.value.handleStepSelected=="function"?(console.log("[DirectView] Forwarding step selection to right panel:",b,A),w.value.handleStepSelected(b,A)):console.warn("[DirectView] rightPanelRef.handleStepSelected method not available")},N=(b,A,c,_)=>{console.log("[DirectView] Sub plan step selected:",{parentPlanId:b,subPlanId:A,stepIndex:c,subStepIndex:_}),w.value&&typeof w.value.handleSubPlanStepSelected=="function"?(console.log("[DirectView] Forwarding sub plan step selection to right panel:",{parentPlanId:b,subPlanId:A,stepIndex:c,subStepIndex:_}),w.value.handleSubPlanStepSelected(b,A,c,_)):console.warn("[DirectView] rightPanelRef.handleSubPlanStepSelected method not available")},te=()=>{console.log("[DirectView] Plan mode button clicked"),f.toggleSidebar(),console.log("[DirectView] Sidebar toggled, isCollapsed:",f.isCollapsed)},oe=()=>{s.push("/home")},ve=()=>{s.push("/configs")},ue=async b=>{var c,_,t,n;if(console.log("[DirectView] Plan execution requested:",b),Y.value){console.log("[DirectView] Plan execution already in progress, ignoring request");return}Y.value=!0;let A=!1;T.value&&typeof T.value.addMessage=="function"?(console.log("[DirectView] Calling chatRef.addMessage for plan execution:",b.title),T.value.addMessage("user",b.title),A=!0):console.warn("[DirectView] chatRef.addMessage method not available");try{const i=((c=b.planData)==null?void 0:c.planTemplateId)||((_=b.planData)==null?void 0:_.id)||((t=b.planData)==null?void 0:t.planId);if(!i)throw new Error("没有找到计划模板ID");console.log("[Direct] Executing plan with templateId:",i,"params:",b.params),console.log("[Direct] About to call PlanActApiService.executePlan");let d;if((n=b.params)!=null&&n.trim()?(console.log("[Direct] Calling executePlan with params:",b.params.trim()),d=await xe.executePlan(i,b.params.trim())):(console.log("[Direct] Calling executePlan without params"),d=await xe.executePlan(i)),console.log("[Direct] Plan execution API response:",d),d.planId)console.log("[Direct] Got planId from response:",d.planId,"starting plan execution"),q.value=d.planId,console.log("[Direct] Set currentRootPlanId to:",d.planId),console.log("[Direct] Delegating plan execution to planExecutionManager"),ae.handlePlanExecutionRequested(d.planId,b.title);else throw console.error("[Direct] No planId in response:",d),new Error("执行计划失败：未返回有效的计划ID")}catch(i){console.error("[Direct] Plan execution failed:",i),console.error("[Direct] Error details:",{message:i.message,stack:i.stack}),q.value=null,T.value&&typeof T.value.addMessage=="function"?(console.log("[Direct] Adding error messages to chat"),A||T.value.addMessage("user",b.title),T.value.addMessage("assistant",`执行计划失败: ${i.message||"未知错误"}`,{thinking:void 0})):(console.error("[Direct] Chat ref not available, showing alert"),alert(`执行计划失败: ${i.message||"未知错误"}`))}finally{console.log("[Direct] Plan execution finished, resetting isExecutingPlan flag"),Y.value=!1}};return(b,A)=>(h(),g("div",Nl,[e("div",Ul,[k(hn,{onPlanExecutionRequested:ue}),e("div",{class:"left-panel",style:Me({width:C.value+"%"})},[e("div",Ll,[e("button",{class:"back-button",onClick:oe},[k(o($),{icon:"carbon:arrow-left"})]),e("h2",null,a(b.$t("conversation")),1),e("div",Vl,[k(dt),e("button",{class:"config-button",onClick:ve,title:b.$t("direct.configuration")},[k(o($),{icon:"carbon:settings-adjust",width:"20"})],8,ql),e("button",{class:"cron-task-btn",onClick:A[0]||(A[0]=c=>r.value=!0),title:b.$t("cronTask.title")},[k(o($),{icon:"carbon:alarm",width:"20"})],8,Fl)])]),e("div",Ol,[k(ca,{ref_key:"chatRef",ref:T,mode:"direct","initial-prompt":m.value||"",onStepSelected:y,onSubPlanStepSelected:N},null,8,["initial-prompt"])]),(h(),de(va,{key:b.$i18n.locale,ref_key:"inputRef",ref:I,disabled:X.value,placeholder:X.value?o(L)("input.waiting"):o(L)("input.placeholder"),"initial-value":m.value,onSend:S,onClear:P,onFocus:F,onUpdateState:M,onPlanModeClicked:te},null,8,["disabled","placeholder","initial-value"]))],4),e("div",{class:"panel-resizer",onMousedown:R,onDblclick:le,title:b.$t("direct.panelResizeHint")},A[2]||(A[2]=[e("div",{class:"resizer-line"},null,-1)]),40,Bl),k(Is,{ref_key:"rightPanelRef",ref:w,style:Me({width:100-C.value+"%"})},null,8,["style"])]),k(Ml,{modelValue:r.value,"onUpdate:modelValue":A[1]||(A[1]=c=>r.value=c)},null,8,["modelValue"])]))}}),Kl=Se(Wl,[["__scopeId","data-v-4b5db98e"]]);export{Kl as default};
