var Le=Object.defineProperty;var Be=(I,o,n)=>o in I?Le(I,o,{enumerable:!0,configurable:!0,writable:!0,value:n}):I[o]=n;var ge=(I,o,n)=>Be(I,typeof o!="symbol"?o+"":o,n);import{d as ae,c as u,o as d,e,t as s,f as fe,r as L,s as te,g as ee,h as be,i as _,b as R,p as b,w as q,v as G,F as H,j as Y,m as oe,C as xe,a as ne,y as J,T as we,H as $e,A as Ie,u as t,q as _e,z as Ve,B as qe,l as Oe,E as je,n as Ge,I as Ae,J as Je,D as Ke,K as ze,k as He}from"./index-F6gaFRaU.js";import{_ as le,I as S}from"./_plugin-vue_export-helper-BA3blOW-.js";import{u as Me}from"./useToast-CPP-L7L-.js";import{L as Ye}from"./index-CEaPGpY_.js";const We={class:"switch"},Xe=["checked"],Qe={class:"switch-label"},Ze=ae({__name:"index",props:{enabled:{type:Boolean},label:{}},emits:["update:switchValue"],setup(I,{emit:o}){const n=o,p=v=>{const g=v.target.checked;n("update:switchValue",g)};return(v,g)=>(d(),u("label",We,[e("input",{type:"checkbox",checked:v.enabled,onChange:p},null,40,Xe),g[0]||(g[0]=e("span",{class:"slider"},null,-1)),e("span",Qe,s(v.label),1)]))}}),eo=le(Ze,[["__scopeId","data-v-d484b4a3"]]);class Ce{static async getConfigsByGroup(o){try{const n=await fetch(`${this.BASE_URL}/group/${o}`);if(!n.ok)throw new Error(`Failed to get ${o} group configuration: ${n.status}`);return await n.json()}catch(n){throw console.error(`Failed to get ${o} group configuration:`,n),n}}static async batchUpdateConfigs(o){if(o.length===0)return{success:!0,message:"No configuration needs to be updated"};try{const n=await fetch(`${this.BASE_URL}/batch-update`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(o)});if(!n.ok)throw new Error(`Batch update configuration failed: ${n.status}`);return{success:!0,message:"Configuration saved successfully"}}catch(n){return console.error("Batch update configuration failed:",n),{success:!1,message:n instanceof Error?n.message:"Update failed, please try again"}}}static async getConfigById(o){try{const n=await fetch(`${this.BASE_URL}/${o}`);if(!n.ok)throw new Error(`Failed to get configuration item: ${n.status}`);return await n.json()}catch(n){throw console.error(`Failed to get configuration item[${o}]:`,n),n}}static async updateConfig(o){try{const n=await fetch(`${this.BASE_URL}/${o.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(o)});if(!n.ok)throw new Error(`Failed to update configuration item: ${n.status}`);return{success:!0,message:"Configuration updated successfully"}}catch(n){return console.error("Failed to update configuration item:",n),{success:!1,message:n instanceof Error?n.message:"Update failed, please try again"}}}}ge(Ce,"BASE_URL","/api/config");const oo={class:"config-panel"},to={class:"config-header"},so={class:"header-left"},no={class:"config-stats"},ao={class:"stat-item"},lo={class:"stat-label"},io={class:"stat-value"},co={key:0,class:"stat-item"},ro={class:"stat-label"},uo={class:"stat-value modified"},po={class:"header-right"},fo={class:"import-export-actions"},go=["title"],mo=["title"],ho={class:"search-box"},vo=["placeholder"],yo={key:0,class:"loading-container"},_o={key:1,class:"config-groups"},bo={class:"group-header"},Co={class:"group-info"},$o={class:"group-icon"},wo={class:"group-actions"},ko=["onClick","disabled","title"],So={class:"sub-groups"},To=["onClick"],No={class:"sub-group-info"},Vo={class:"sub-group-title"},Ao={class:"item-count"},Uo={class:"config-items"},xo={key:0,class:"config-item-content vertical-layout"},Po={class:"config-item-info"},Eo={class:"config-item-header"},Do={class:"config-label"},Mo={class:"type-badge boolean"},Fo={key:0,class:"modified-badge"},Ro=["title"],Lo={class:"config-control"},Bo=["value","onChange"],Io=["value"],qo={key:1,class:"config-item-content vertical-layout"},Oo={class:"config-item-info"},jo={class:"config-item-header"},Go={class:"config-label"},Jo={class:"type-badge select"},Ko={key:0,class:"modified-badge"},zo=["title"],Ho={class:"config-control"},Yo=["value","onChange"],Wo=["value"],Xo={key:2,class:"config-item-content vertical-layout"},Qo={class:"config-item-info"},Zo={class:"config-item-header"},et={class:"config-label"},ot={class:"type-badge textarea"},tt={key:0,class:"modified-badge"},st=["title"],nt={class:"config-control"},at=["value","onInput"],lt={key:3,class:"config-item-content vertical-layout"},it={class:"config-item-info"},ct={class:"config-item-header"},rt={class:"config-label"},dt={class:"type-badge number"},ut={key:0,class:"modified-badge"},pt=["title"],ft={key:0,class:"config-meta"},gt={class:"range-info"},mt={class:"config-control"},ht=["value","onInput","min","max"],vt={key:4,class:"config-item-content vertical-layout"},yt={class:"config-item-info"},_t={class:"config-item-header"},bt={class:"config-label"},Ct={class:"type-badge string"},$t={key:0,class:"modified-badge"},wt=["title"],kt={class:"config-control"},St=["value","onInput"],Tt={key:2,class:"empty-state"},Nt=ae({__name:"basicConfig",setup(I){const{t:o}=fe(),n=L(!0),p=L(!1),v=L([]),g=L(new Map),m=L(new Set),c=te({show:!1,text:"",type:"success"}),$=L(""),T={headless:"config.basicConfig.browserSettings.headless",requestTimeout:"config.basicConfig.browserSettings.requestTimeout",debugDetail:"config.basicConfig.general.debugDetail",baseDir:"config.basicConfig.general.baseDir",openBrowser:"config.basicConfig.interactionSettings.openBrowser",maxSteps:"config.basicConfig.agentSettings.maxSteps",userInputTimeout:"config.basicConfig.agentSettings.userInputTimeout",maxMemory:"config.basicConfig.agentSettings.maxMemory",parallelToolCalls:"config.basicConfig.agentSettings.parallelToolCalls",forceOverrideFromYaml:"config.basicConfig.agents.forceOverrideFromYaml",enabled:"config.basicConfig.infiniteContext.enabled",parallelThreads:"config.basicConfig.infiniteContext.parallelThreads",taskContextSize:"config.basicConfig.infiniteContext.taskContextSize",allowExternalAccess:"config.basicConfig.fileSystem.allowExternalAccess"},E={manus:"config.basicConfig.groupDisplayNames.manus"},M={manus:"🤖",browser:"🌐",interaction:"🖥️",system:"⚙️",performance:"⚡"},A={agent:"config.subGroupDisplayNames.agent",browser:"config.subGroupDisplayNames.browser",interaction:"config.subGroupDisplayNames.interaction",agents:"config.subGroupDisplayNames.agents",infiniteContext:"config.subGroupDisplayNames.infiniteContext",general:"config.subGroupDisplayNames.general",filesystem:"config.subGroupDisplayNames.filesystem"},x=ee(()=>v.value.some(l=>l.subGroups.some(C=>C.items.some(k=>g.value.get(k.id)!==k.configValue)))),W=l=>l==="true",O=l=>parseFloat(l)||0,N=l=>({maxSteps:1,browserTimeout:1,maxThreads:1,timeoutSeconds:5,maxMemory:1})[l]||1,F=l=>({maxSteps:100,browserTimeout:600,maxThreads:32,timeoutSeconds:300,maxMemory:1e3})[l]||1e4,X=l=>typeof l=="string"?l:l.value,se=l=>typeof l=="string"?l:l.label,V=(l,C)=>{if(typeof C=="boolean")return C.toString();if(typeof C=="string"){if(l.options&&l.options.length>0){const k=l.options.find(w=>(typeof w=="string"?w:w.label)===C||(typeof w=="string"?w:w.value)===C);if(k)return typeof k=="string"?k:k.value}return C}return String(C)},U=(l,C,k=!1)=>{let w;l.inputType==="BOOLEAN"||l.inputType==="CHECKBOX"?w=V(l,C):w=String(C),l.configValue!==w&&(l.configValue=w,l._modified=!0,(k||l.inputType==="BOOLEAN"||l.inputType==="CHECKBOX"||l.inputType==="SELECT")&&i())};let h=null;const i=()=>{h&&clearTimeout(h),h=window.setTimeout(()=>{D()},500)},a=(l,C="success")=>{c.text=l,c.type=C,c.show=!0,setTimeout(()=>{c.show=!1},3e3)},r=async()=>{try{n.value=!0;const C=["manus"].map(async w=>{try{const f=await Ce.getConfigsByGroup(w);if(f.length===0)return null;const B=f.map(Z=>({...Z,displayName:T[Z.configKey]||Z.configKey,min:N(Z.configKey),max:F(Z.configKey)}));B.forEach(Z=>{g.value.set(Z.id,Z.configValue)});const j=new Map;B.forEach(Z=>{const re=Z.configSubGroup??"general";j.has(re)||j.set(re,[]),j.get(re).push(Z)});const Q=Array.from(j.entries()).map(([Z,re])=>({name:Z,displayName:A[Z]||Z,items:re}));return{name:w,displayName:E[w]||w,subGroups:Q}}catch(f){return console.warn(`加载配置组 ${w} 失败，跳过:`,f),null}}),k=await Promise.all(C);v.value=k.filter(w=>w!==null),console.log(o("config.basicConfig.loadConfigSuccess"),v.value)}catch(l){console.error(o("config.basicConfig.loadConfigFailed"),l),a(o("config.basicConfig.loadConfigFailed"),"error")}finally{n.value=!1}},D=async()=>{if(!(p.value||!x.value))try{p.value=!0;const l=[];if(v.value.forEach(k=>{k.subGroups.forEach(w=>{const f=w.items.filter(B=>B._modified);l.push(...f)})}),l.length===0){a(o("config.basicConfig.noModified"));return}const C=await Ce.batchUpdateConfigs(l);C.success?(l.forEach(k=>{g.value.set(k.id,k.configValue),k._modified=!1}),a(o("config.basicConfig.saveSuccess"))):a(C.message||o("config.basicConfig.saveFailed"),"error")}catch(l){console.error(o("config.basicConfig.saveFailed"),l),a(o("config.basicConfig.saveFailed"),"error")}finally{p.value=!1}},z=async l=>{if(confirm(o("config.basicConfig.resetGroupConfirm",E[l]||l)))try{p.value=!0;const k=v.value.find(B=>B.name===l);if(!k)return;const w=[];if(k.subGroups.forEach(B=>{B.items.forEach(j=>{const Q=ce(j.configKey);Q!==j.configValue&&w.push({...j,configValue:Q})})}),w.length===0){a(o("config.basicConfig.isDefault"));return}const f=await Ce.batchUpdateConfigs(w);f.success?(await r(),a(o("config.basicConfig.resetSuccess",w.length))):a(f.message||o("config.basicConfig.resetFailed"),"error")}catch(k){console.error(o("config.basicConfig.resetFailed"),k),a(o("config.basicConfig.resetFailed"),"error")}finally{p.value=!1}},ce=l=>({systemName:"JTaskPilot",language:"zh-CN",maxThreads:"8",timeoutSeconds:"60",autoOpenBrowser:"false",headlessBrowser:"true",maxMemory:"1000"})[l]||"",me=(l,C)=>{const k=`${l}-${C}`;m.value.has(k)?m.value.delete(k):m.value.add(k)},ie=(l,C)=>m.value.has(`${l}-${C}`),he=ee(()=>{const l=v.value.reduce((k,w)=>k+w.subGroups.reduce((f,B)=>f+B.items.length,0),0),C=v.value.reduce((k,w)=>k+w.subGroups.reduce((f,B)=>f+B.items.filter(j=>g.value.get(j.id)!==j.configValue).length,0),0);return{total:l,modified:C}}),P=ee(()=>{if(!$.value.trim())return v.value;const l=$.value.toLowerCase();return v.value.map(C=>({...C,subGroups:C.subGroups.map(k=>({...k,items:k.items.filter(w=>w.displayName.toLowerCase().includes(l)||w.configKey.toLowerCase().includes(l)||w.description&&w.description.toLowerCase().includes(l))})).filter(k=>k.items.length>0)})).filter(C=>C.subGroups.length>0)}),y=()=>{try{const l={timestamp:new Date().toISOString(),version:"1.0",configs:v.value.reduce((f,B)=>(B.subGroups.forEach(j=>{j.items.forEach(Q=>{f[Q.configKey]=Q.configValue})}),f),{})},C=JSON.stringify(l,null,2),k=new Blob([C],{type:"application/json"}),w=document.createElement("a");w.href=URL.createObjectURL(k),w.download=`config-export-${new Date().toISOString().split("T")[0]}.json`,w.click(),a(o("config.basicConfig.exportSuccess"))}catch(l){console.error(o("config.basicConfig.exportFailed"),l),a(o("config.basicConfig.exportFailed"),"error")}},K=l=>{var f;const C=l.target,k=(f=C.files)==null?void 0:f[0];if(!k)return;const w=new FileReader;w.onload=async B=>{var j;try{const Q=JSON.parse((j=B.target)==null?void 0:j.result);if(!Q.configs)throw new Error(o("config.basicConfig.invalidFormat"));if(!confirm(o("config.importConfirm")))return;p.value=!0;const re=[];if(v.value.forEach(Fe=>{Fe.subGroups.forEach(Re=>{Re.items.forEach(Ne=>{Object.prototype.hasOwnProperty.call(Q.configs,Ne.configKey)&&re.push({...Ne,configValue:Q.configs[Ne.configKey]})})})}),re.length===0){a(o("config.basicConfig.notFound"));return}const Ee=await Ce.batchUpdateConfigs(re);Ee.success?(await r(),a(o("config.basicConfig.importSuccess"))):a(Ee.message||o("config.basicConfig.importFailed"),"error")}catch(Q){console.error(o("config.basicConfig.importFailed"),Q),a(o("config.basicConfig.importFailed"),"error")}finally{p.value=!1,C.value=""}},w.readAsText(k)};return be(()=>{r()}),(l,C)=>(d(),u("div",oo,[e("div",to,[e("div",so,[e("h2",null,s(l.$t("config.basicConfig.title")),1),e("div",no,[e("span",ao,[e("span",lo,s(l.$t("config.basicConfig.totalConfigs"))+":",1),e("span",io,s(he.value.total),1)]),he.value.modified>0?(d(),u("span",co,[e("span",ro,s(l.$t("config.basicConfig.modified"))+":",1),e("span",uo,s(he.value.modified),1)])):R("",!0)])]),e("div",po,[e("div",fo,[e("button",{onClick:y,class:"action-btn",title:l.$t("config.basicConfig.exportConfigs")}," 📤 ",8,go),e("label",{class:"action-btn",title:l.$t("config.basicConfig.importConfigs")},[C[1]||(C[1]=b(" 📥 ")),e("input",{type:"file",accept:".json",onChange:K,style:{display:"none"}},null,32)],8,mo)]),e("div",ho,[q(e("input",{"onUpdate:modelValue":C[0]||(C[0]=k=>$.value=k),type:"text",placeholder:l.$t("config.search"),class:"search-input"},null,8,vo),[[G,$.value]]),C[2]||(C[2]=e("span",{class:"search-icon"},"🔍",-1))])])]),n.value?(d(),u("div",yo,[C[3]||(C[3]=e("div",{class:"loading-spinner"},null,-1)),e("p",null,s(l.$t("config.loading")),1)])):P.value.length>0?(d(),u("div",_o,[(d(!0),u(H,null,Y(P.value,k=>(d(),u("div",{key:k.name,class:"config-group"},[e("div",bo,[e("div",Co,[e("span",$o,s(M[k.name]||"⚙️"),1)]),e("div",wo,[e("button",{onClick:w=>z(k.name),class:"reset-btn",disabled:p.value,title:l.$t("config.resetGroupConfirm")},s(l.$t("config.reset")),9,ko)]),C[4]||(C[4]=e("div",{class:"group-divider"},null,-1))]),e("div",So,[(d(!0),u(H,null,Y(k.subGroups,w=>(d(),u("div",{key:w.name,class:"sub-group"},[e("div",{class:"sub-group-header",onClick:f=>me(k.name,w.name)},[e("div",No,[C[5]||(C[5]=e("span",{class:"sub-group-icon"},"📁",-1)),e("h4",Vo,s(l.$t(w.displayName)),1),e("span",Ao,"("+s(w.items.length)+")",1)]),e("span",{class:oe(["collapse-icon",{collapsed:ie(k.name,w.name)}])}," ▼ ",2)],8,To),q(e("div",Uo,[(d(!0),u(H,null,Y(w.items,f=>(d(),u("div",{key:f.id,class:oe(["config-item",{modified:g.value.get(f.id)!==f.configValue}])},[f.inputType==="BOOLEAN"||f.inputType==="CHECKBOX"?(d(),u("div",xo,[e("div",Po,[e("div",Eo,[e("label",Do,[b(s(l.$t(f.displayName)||f.description)+" ",1),e("span",Mo,s(f.inputType==="CHECKBOX"?l.$t("config.types.checkbox"):l.$t("config.types.boolean")),1),g.value.get(f.id)!==f.configValue?(d(),u("span",Fo,s(l.$t("config.modified")),1)):R("",!0)]),e("span",{class:"config-key",title:f.configKey},s(f.configKey),9,Ro)])]),e("div",Lo,[f.options&&f.options.length>0?(d(),u("select",{key:0,class:"config-input select-input",value:f.configValue,onChange:B=>{var j;return U(f,((j=B.target)==null?void 0:j.value)||"")}},[(d(!0),u(H,null,Y(f.options,B=>(d(),u("option",{key:X(B),value:X(B)},s(se(B)),9,Io))),128))],40,Bo)):(d(),ne(eo,{key:1,enabled:W(f.configValue),label:"","onUpdate:switchValue":B=>U(f,B)},null,8,["enabled","onUpdate:switchValue"]))])])):f.inputType==="SELECT"?(d(),u("div",qo,[e("div",Oo,[e("div",jo,[e("label",Go,[b(s(l.$t(f.displayName)||f.description)+" ",1),e("span",Jo,s(l.$t("config.types.select")),1),g.value.get(f.id)!==f.configValue?(d(),u("span",Ko,s(l.$t("config.modified")),1)):R("",!0)]),e("span",{class:"config-key",title:f.configKey},s(f.configKey),9,zo)])]),e("div",Ho,[e("select",{class:"config-input select-input",value:f.configValue,onChange:B=>{var j;return U(f,((j=B.target)==null?void 0:j.value)||"")}},[(d(!0),u(H,null,Y(f.options||[],B=>(d(),u("option",{key:X(B),value:X(B)},s(se(B)),9,Wo))),128))],40,Yo)])])):f.inputType==="TEXTAREA"?(d(),u("div",Xo,[e("div",Qo,[e("div",Zo,[e("label",et,[b(s(l.$t(f.displayName)||f.description)+" ",1),e("span",ot,s(l.$t("config.types.textarea")),1),g.value.get(f.id)!==f.configValue?(d(),u("span",tt,s(l.$t("config.modified")),1)):R("",!0)]),e("span",{class:"config-key",title:f.configKey},s(f.configKey),9,st)])]),e("div",nt,[e("textarea",{class:"config-input textarea-input",value:f.configValue,onInput:B=>{var j;return U(f,((j=B.target)==null?void 0:j.value)||"")},onBlur:i,rows:"3"},null,40,at)])])):f.inputType==="NUMBER"?(d(),u("div",lt,[e("div",it,[e("div",ct,[e("label",rt,[b(s(l.$t(f.displayName)||f.description)+" ",1),e("span",dt,s(l.$t("config.types.number")),1),g.value.get(f.id)!==f.configValue?(d(),u("span",ut,s(l.$t("config.modified")),1)):R("",!0)]),e("span",{class:"config-key",title:f.configKey},s(f.configKey),9,pt),f.min||f.max?(d(),u("div",ft,[e("span",gt,s(l.$t("config.range"))+": "+s(f.min||0)+" - "+s(f.max||"∞"),1)])):R("",!0)])]),e("div",mt,[e("input",{type:"number",class:"config-input number-input",value:O(f.configValue),onInput:B=>{var j;return U(f,((j=B.target)==null?void 0:j.value)||"")},onBlur:i,min:f.min||1,max:f.max||1e4},null,40,ht)])])):(d(),u("div",vt,[e("div",yt,[e("div",_t,[e("label",bt,[b(s(l.$t(f.displayName)||f.description)+" ",1),e("span",Ct,s(f.inputType==="TEXT"?l.$t("config.types.text"):l.$t("config.types.string")),1),g.value.get(f.id)!==f.configValue?(d(),u("span",$t,s(l.$t("config.modified")),1)):R("",!0)]),e("span",{class:"config-key",title:f.configKey},s(f.configKey),9,wt)])]),e("div",kt,[e("input",{type:"text",class:"config-input text-input",value:f.configValue,onInput:B=>{var j;return U(f,((j=B.target)==null?void 0:j.value)||"")},onBlur:i},null,40,St)])]))],2))),128))],512),[[xe,!ie(k.name,w.name)]])]))),128))])]))),128))])):(d(),u("div",Tt,[e("p",null,s(l.$t("config.notFound")),1)])),_(we,{name:"message-fade"},{default:J(()=>[c.show?(d(),u("div",{key:0,class:oe(["message-toast",c.type])},s(c.text),3)):R("",!0)]),_:1})]))}}),De=le(Nt,[["__scopeId","data-v-bb27f059"]]),Vt={},At={class:"config-config"},Ut={class:"panel-header"},xt={class:"panel-actions"};function Pt(I,o){return d(),u("div",At,[e("div",Ut,[$e(I.$slots,"title",{},void 0),e("div",xt,[$e(I.$slots,"actions",{},void 0)])]),$e(I.$slots,"default",{},void 0)])}const Te=le(Vt,[["render",Pt],["__scopeId","data-v-c91688e7"]]),Et={class:"modal-header"},Dt={class:"modal-content"},Mt={class:"modal-footer"},Ft=ae({__name:"index",props:{modelValue:{type:Boolean,required:!0},title:{type:String,default:""}},emits:["update:modelValue","confirm"],setup(I){const o=n=>{n.target===n.currentTarget&&(n.stopPropagation(),n.preventDefault())};return(n,p)=>(d(),ne(Ie,{to:"body"},[_(we,{name:"modal"},{default:J(()=>[I.modelValue?(d(),u("div",{key:0,class:"modal-overlay",onClick:o},[e("div",{class:"modal-container",onClick:p[3]||(p[3]=_e(()=>{},["stop"]))},[e("div",Et,[e("h3",null,s(I.title),1),e("button",{class:"close-btn",onClick:p[0]||(p[0]=v=>n.$emit("update:modelValue",!1))},[_(t(S),{icon:"carbon:close"})])]),e("div",Dt,[$e(n.$slots,"default",{},void 0,!0)]),e("div",Mt,[$e(n.$slots,"footer",{},()=>[e("button",{class:"cancel-btn",onClick:p[1]||(p[1]=v=>n.$emit("update:modelValue",!1))},s(n.$t("common.cancel")),1),e("button",{class:"confirm-btn",onClick:p[2]||(p[2]=v=>n.$emit("confirm"))},s(n.$t("common.confirm")),1)],!0)])])])):R("",!0)]),_:3})]))}}),ue=le(Ft,[["__scopeId","data-v-baaf1c89"]]),Rt={class:"tool-selection-content"},Lt={class:"tool-controls"},Bt={class:"search-container"},It={class:"sort-container"},qt={class:"tool-summary"},Ot={class:"summary-text"},jt={key:0,class:"tool-groups"},Gt=["onClick"],Jt={class:"group-title-area"},Kt={class:"group-name"},zt={class:"group-count"},Ht={class:"group-enable-all"},Yt=["checked","onChange","data-group"],Wt={class:"tool-info"},Xt={class:"tool-selection-name"},Qt={key:0,class:"tool-selection-desc"},Zt={class:"tool-actions"},es=["checked","onChange"],os={key:1,class:"empty-state"},ts=ae({__name:"index",props:{modelValue:{type:Boolean},tools:{},selectedToolIds:{}},emits:["update:modelValue","confirm"],setup(I,{emit:o}){const n=I,p=o,v=ee({get:()=>n.modelValue,set:h=>p("update:modelValue",h)}),g=L(""),m=L("group"),c=L(new Set),$=L([]),T=(h,i)=>{const a=document.querySelector(`input[data-group="${h}"]`);a&&(a.indeterminate=F(i))};Ve(()=>n.selectedToolIds,h=>{$.value=[...h]},{immediate:!0});const E=ee(()=>{let h=n.tools.filter(i=>i.key);if(g.value){const i=g.value.toLowerCase();h=h.filter(a=>{var r;return a.name.toLowerCase().includes(i)||a.description.toLowerCase().includes(i)||(((r=a.serviceGroup)==null?void 0:r.toLowerCase().includes(i))??!1)})}switch(m.value){case"name":h=[...h].sort((i,a)=>i.name.localeCompare(a.name));break;case"enabled":h=[...h].sort((i,a)=>{const r=$.value.includes(i.key),D=$.value.includes(a.key);return r&&!D?-1:!r&&D?1:i.name.localeCompare(a.name)});break;case"group":default:h=[...h].sort((i,a)=>{const r=i.serviceGroup??"未分组",D=a.serviceGroup??"未分组";return r!==D?r.localeCompare(D):i.name.localeCompare(a.name)});break}return h}),M=ee(()=>{const h=new Map;return E.value.forEach(i=>{const a=i.serviceGroup??"未分组";h.has(a)||h.set(a,[]),h.get(a).push(i)}),new Map([...h.entries()].sort())}),A=ee(()=>E.value.length);Ve([$,M],()=>{Oe(()=>{for(const[h,i]of M.value)T(h,i)})},{flush:"post",deep:!1});const x=h=>$.value.includes(h),W=(h,i)=>{i.stopPropagation();const r=i.target.checked;if(!h){console.error("toolKey is undefined, cannot proceed");return}r?$.value.includes(h)||($.value=[...$.value,h]):$.value=$.value.filter(D=>D!==h)},O=h=>h.filter(i=>$.value.includes(i.key)),N=h=>h.length>0&&h.every(i=>$.value.includes(i.key)),F=h=>{const i=O(h).length;return i>0&&i<h.length},X=(h,i)=>{i.stopPropagation();const r=i.target.checked,D=h.map(z=>z.key);if(r){const z=[...$.value];D.forEach(ce=>{z.includes(ce)||z.push(ce)}),$.value=z}else $.value=$.value.filter(z=>!D.includes(z))},se=h=>{c.value.has(h)?c.value.delete(h):c.value.add(h)},V=()=>{p("confirm",[...$.value]),p("update:modelValue",!1)},U=()=>{$.value=[...n.selectedToolIds],p("update:modelValue",!1)};return Ve(v,h=>{if(h){c.value.clear();const i=Array.from(M.value.keys());i.length>1&&i.slice(1).forEach(a=>{c.value.add(a)})}}),(h,i)=>(d(),ne(ue,{modelValue:v.value,"onUpdate:modelValue":[i[4]||(i[4]=a=>v.value=a),U],title:"选择工具",onConfirm:V},{default:J(()=>[e("div",Rt,[e("div",Lt,[e("div",Bt,[q(e("input",{"onUpdate:modelValue":i[0]||(i[0]=a=>g.value=a),type:"text",class:"search-input",placeholder:"搜索工具..."},null,512),[[G,g.value]])]),e("div",It,[q(e("select",{"onUpdate:modelValue":i[1]||(i[1]=a=>m.value=a),class:"sort-select"},i[5]||(i[5]=[e("option",{value:"group"},"按服务组排序",-1),e("option",{value:"name"},"按名称排序",-1),e("option",{value:"enabled"},"按启用状态排序",-1)]),512),[[qe,m.value]])])]),e("div",qt,[e("span",Ot," 共 "+s(M.value.size)+" 个服务组，"+s(A.value)+" 个工具 (已选择 "+s($.value.length)+" 个) ",1)]),M.value.size>0?(d(),u("div",jt,[(d(!0),u(H,null,Y(M.value,([a,r])=>(d(),u("div",{key:a,class:"tool-group"},[e("div",{class:oe(["tool-group-header",{collapsed:c.value.has(a)}]),onClick:D=>se(a)},[e("div",Jt,[_(t(S),{icon:c.value.has(a)?"carbon:chevron-right":"carbon:chevron-down",class:"collapse-icon"},null,8,["icon"]),_(t(S),{icon:"carbon:folder",class:"group-icon"}),e("span",Kt,s(a),1),e("span",zt," ("+s(O(r).length)+"/"+s(r.length)+") ",1)]),e("div",{class:"group-actions",onClick:i[2]||(i[2]=_e(()=>{},["stop"]))},[e("label",Ht,[e("input",{type:"checkbox",class:"group-enable-checkbox",checked:N(r),onChange:D=>X(r,D),"data-group":a},null,40,Yt),i[6]||(i[6]=e("span",{class:"enable-label"},"启用全部",-1))])])],10,Gt),e("div",{class:oe(["tool-group-content",{collapsed:c.value.has(a)}])},[(d(!0),u(H,null,Y(r.filter(D=>D&&D.key),D=>(d(),u("div",{key:D.key,class:"tool-selection-item"},[e("div",Wt,[e("div",Xt,s(D.name),1),D.description?(d(),u("div",Qt,s(D.description),1)):R("",!0)]),e("div",Zt,[e("label",{class:"tool-enable-switch",onClick:i[3]||(i[3]=_e(()=>{},["stop"]))},[e("input",{type:"checkbox",class:"tool-enable-checkbox",checked:x(D.key),onChange:z=>W(D.key,z)},null,40,es),i[7]||(i[7]=e("span",{class:"tool-enable-slider"},null,-1))])])]))),128))],2)]))),128))])):(d(),u("div",os,[_(t(S),{icon:"carbon:tools",class:"empty-icon"}),i[8]||(i[8]=e("p",null,"没有找到工具",-1))]))])]),_:1},8,["modelValue"]))}}),ss=le(ts,[["__scopeId","data-v-79f6a572"]]);class pe{static async handleResponse(o){if(!o.ok)try{const n=await o.json();throw new Error(n.message||`API request failed: ${o.status}`)}catch{throw new Error(`API request failed: ${o.status} ${o.statusText}`)}return o}static async getAllAgents(o){try{if(o){const n=await fetch(`${this.BASE_URL}/namespace/${o}`);return await(await this.handleResponse(n)).json()}else{const n=await fetch(`${this.BASE_URL}`);return await(await this.handleResponse(n)).json()}}catch(n){throw console.error("Failed to get Agent list:",n),n}}static async getAgentById(o){try{const n=await fetch(`${this.BASE_URL}/${o}`);return await(await this.handleResponse(n)).json()}catch(n){throw console.error(`Failed to get Agent[${o}] details:`,n),n}}static async createAgent(o){try{const n=await fetch(this.BASE_URL,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(o)});return await(await this.handleResponse(n)).json()}catch(n){throw console.error("Failed to create Agent:",n),n}}static async updateAgent(o,n){try{const p=await fetch(`${this.BASE_URL}/${o}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(n)});return await(await this.handleResponse(p)).json()}catch(p){throw console.error(`Failed to update Agent[${o}]:`,p),p}}static async deleteAgent(o){try{const n=await fetch(`${this.BASE_URL}/${o}`,{method:"DELETE"});if(n.status===400)throw new Error("Cannot delete default Agent");await this.handleResponse(n)}catch(n){throw console.error(`Failed to delete Agent[${o}]:`,n),n}}static async getAvailableTools(){try{const o=await fetch(`${this.BASE_URL}/tools`);return await(await this.handleResponse(o)).json()}catch(o){throw console.error("Failed to get available tools list:",o),o}}}ge(pe,"BASE_URL","/api/agents");class de{static async handleResponse(o){if(!o.ok)try{const n=await o.json();throw new Error(n.message||`API request failed: ${o.status}`)}catch{throw new Error(`API request failed: ${o.status} ${o.statusText}`)}return o}static async getAllModels(){try{const o=await fetch(this.BASE_URL);return await(await this.handleResponse(o)).json()}catch(o){throw console.error("Failed to get Model list:",o),o}}static async getAllTypes(){try{const o=await fetch(`${this.BASE_URL}/types`);return await(await this.handleResponse(o)).json()}catch(o){throw console.error("Failed to get Model list:",o),o}}static async getModelById(o){try{const n=await fetch(`${this.BASE_URL}/${o}`);return await(await this.handleResponse(n)).json()}catch(n){throw console.error(`Failed to get Model[${o}] details:`,n),n}}static async createModel(o){try{const n=await fetch(this.BASE_URL,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(o)});return await(await this.handleResponse(n)).json()}catch(n){throw console.error("Failed to create Model:",n),n}}static async updateModel(o,n){try{const p=await fetch(`${this.BASE_URL}/${o}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(n)});if(p.status===499)throw new Error("Request rejected, please modify the model configuration in the configuration file");return await(await this.handleResponse(p)).json()}catch(p){throw console.error(`Failed to update Model[${o}]:`,p),p}}static async deleteModel(o){try{const n=await fetch(`${this.BASE_URL}/${o}`,{method:"DELETE"});if(n.status===400)throw new Error("Cannot delete default Model");if(n.status===499)throw new Error("Request rejected, please modify the model configuration in the configuration file");await this.handleResponse(n)}catch(n){throw console.error(`Failed to delete Model[${o}]:`,n),n}}}ge(de,"BASE_URL","/api/models");const Pe=je("namespace",()=>{const I=L("default");function o(v){I.value=v}const n=L([{name:"Default",id:"default"}]);function p(v){n.value=v}return{namespace:I,db:n,setNamespace:o,setDb:p}}),ns=["disabled"],as={class:"agent-layout"},ls={class:"agent-list"},is={class:"list-header"},cs={class:"agent-count"},rs={key:0,class:"agents-container"},ds=["onClick"],us={class:"agent-card-header"},ps={class:"agent-name"},fs={class:"agent-desc"},gs={key:0,class:"agent-model"},ms={class:"model-tag"},hs={class:"model-tag"},vs={key:1,class:"agent-tools"},ys={key:0,class:"tool-more"},_s={key:1,class:"loading-state"},bs={key:2,class:"empty-state"},Cs={key:0,class:"agent-detail"},$s={class:"detail-header"},ws={class:"detail-actions"},ks={class:"form-item"},Ss=["placeholder"],Ts={class:"form-item"},Ns=["placeholder"],Vs={class:"form-item"},As=["value","placeholder"],Us={class:"model-section"},xs={class:"form-item"},Ps={class:"model-chooser"},Es=["title"],Ds={key:0,class:"current-model"},Ms={class:"model-type"},Fs={class:"model-name"},Rs={key:1,class:"current-model"},Ls={class:"current-model"},Bs={class:"dropdown-header"},Is={class:"model-options"},qs=["onClick"],Os={class:"model-type"},js={class:"model-name"},Gs={class:"tools-section"},Js={class:"assigned-tools"},Ks={class:"section-header"},zs={class:"tools-grid"},Hs={class:"tool-info"},Ys={class:"tool-name"},Ws={class:"tool-desc"},Xs={key:0,class:"no-tools"},Qs={key:1,class:"no-selection"},Zs={class:"modal-form"},en={class:"form-item"},on=["placeholder"],tn={class:"form-item"},sn=["placeholder"],nn={class:"form-item"},an=["value","placeholder"],ln={class:"delete-confirm"},cn={class:"warning-text"},rn=ae({__name:"agentConfig",setup(I){const{t:o}=fe(),n=Pe(),p=L(!1),v=L(""),g=L(""),m=te([]),c=L(null),$=te([]),T=L(!1),E=L(!1),M=L(!1),A=L(!1),x=L(null),W=te([]),O=()=>{A.value=!A.value},N=P=>{x.value=P,A.value=!1},F=te({name:"",description:"",nextStepPrompt:""}),X=P=>{const y=$.find(K=>K.key===P);return y?y.name:P},se=P=>{const y=$.find(K=>K.key===P);return y?y.description:""},V=(P,y)=>{y==="success"?(g.value=P,setTimeout(()=>{g.value=""},3e3)):(v.value=P,setTimeout(()=>{v.value=""},5e3))},U=async()=>{p.value=!0;try{const[P,y,K]=await Promise.all([pe.getAllAgents(n.namespace),pe.getAvailableTools(),de.getAllModels()]),l=P.map(C=>({...C,availableTools:C.availableTools,...K}));m.splice(0,m.length,...l),$.splice(0,$.length,...y),W.splice(0,W.length,...K),l.length>0&&await h(l[0])}catch(P){console.error("加载数据失败:",P),V(o("config.agentConfig.loadDataFailed")+": "+P.message,"error");const y=[{key:"search-web",name:"网络搜索",description:"在互联网上搜索信息",enabled:!0,serviceGroup:"搜索服务"},{key:"search-local",name:"本地搜索",description:"在本地文件中搜索内容",enabled:!0,serviceGroup:"搜索服务"},{key:"file-read",name:"读取文件",description:"读取本地或远程文件内容",enabled:!0,serviceGroup:"文件服务"},{key:"file-write",name:"写入文件",description:"创建或修改文件内容",enabled:!0,serviceGroup:"文件服务"},{key:"file-delete",name:"删除文件",description:"删除指定的文件",enabled:!1,serviceGroup:"文件服务"},{key:"calculator",name:"计算器",description:"执行数学计算",enabled:!0,serviceGroup:"计算服务"},{key:"code-execute",name:"代码执行",description:"执行Python或JavaScript代码",enabled:!0,serviceGroup:"计算服务"},{key:"weather",name:"天气查询",description:"获取指定地区的天气信息",enabled:!0,serviceGroup:"信息服务"},{key:"currency",name:"汇率查询",description:"查询货币汇率信息",enabled:!0,serviceGroup:"信息服务"},{key:"email",name:"发送邮件",description:"发送电子邮件",enabled:!1,serviceGroup:"通信服务"},{key:"sms",name:"发送短信",description:"发送短信消息",enabled:!1,serviceGroup:"通信服务"}],K=[{id:"demo-1",name:"通用助手",description:"一个能够处理各种任务的智能助手",nextStepPrompt:"You are a helpful assistant that can answer questions and help with various tasks. What would you like me to help you with next?",availableTools:["search-web","calculator","weather"]},{id:"demo-2",name:"数据分析师",description:"专门用于数据分析和可视化的Agent",nextStepPrompt:"You are a data analyst assistant specialized in analyzing data and creating visualizations. Please provide the data you would like me to analyze.",availableTools:["file-read","file-write","calculator","code-execute"]}];$.splice(0,$.length,...y),m.splice(0,m.length,...K),K.length>0&&(c.value=K[0])}finally{p.value=!1}},h=async P=>{try{const y=await pe.getAgentById(P.id);c.value={...y,availableTools:y.availableTools},x.value=y.model??null}catch(y){console.error("加载Agent详情失败:",y),V(o("config.agentConfig.loadDetailsFailed")+": "+y.message,"error"),c.value={...P,availableTools:P.availableTools}}},i=()=>{F.name="",F.description="",F.nextStepPrompt="",T.value=!0},a=async()=>{var P;if(!F.name.trim()||!F.description.trim()){V(o("config.agentConfig.requiredFields"),"error");return}try{const y={name:F.name.trim(),description:F.description.trim(),nextStepPrompt:((P=F.nextStepPrompt)==null?void 0:P.trim())??"",availableTools:[]},K=await pe.createAgent(y);m.push(K),c.value=K,T.value=!1,V(o("config.agentConfig.createSuccess"),"success")}catch(y){V(o("config.agentConfig.createFailed")+": "+y.message,"error")}},r=()=>{M.value=!0},D=P=>{c.value&&(c.value.availableTools=[...P])},z=async()=>{if(c.value){if(!c.value.name.trim()||!c.value.description.trim()){V(o("config.agentConfig.requiredFields"),"error");return}try{c.value.model=x.value;const P=await pe.updateAgent(c.value.id,c.value),y=m.findIndex(K=>K.id===P.id);y!==-1&&(m[y]=P),c.value=P,c.value.model=x.value,V(o("config.agentConfig.saveSuccess"),"success")}catch(P){V(o("config.agentConfig.saveFailed")+": "+P.message,"error")}}},ce=()=>{E.value=!0},me=async()=>{if(c.value)try{await pe.deleteAgent(c.value.id);const P=m.findIndex(y=>y.id===c.value.id);P!==-1&&m.splice(P,1),c.value=m.length>0?m[0]:null,E.value=!1,V(o("config.agentConfig.deleteSuccess"),"success")}catch(P){V(o("config.agentConfig.deleteFailed")+": "+P.message,"error")}},ie=()=>{const P=document.createElement("input");P.type="file",P.accept=".json",P.onchange=y=>{var l;const K=(l=y.target.files)==null?void 0:l[0];if(K){const C=new FileReader;C.onload=async k=>{var w;try{const f=JSON.parse((w=k.target)==null?void 0:w.result);if(!f.name||!f.description)throw new Error(o("config.agentConfig.invalidFormat"));const{id:B,...j}=f,Q=await pe.createAgent(j);m.push(Q),c.value=Q,V(o("config.agentConfig.importSuccess"),"success")}catch(f){V(o("config.agentConfig.importFailed")+": "+f.message,"error")}},C.readAsText(K)}},P.click()},he=()=>{if(c.value)try{const P=JSON.stringify(c.value,null,2),y=new Blob([P],{type:"application/json"}),K=URL.createObjectURL(y),l=document.createElement("a");l.href=K,l.download=`agent-${c.value.name}-${new Date().toISOString().split("T")[0]}.json`,l.click(),URL.revokeObjectURL(K),V(o("config.agentConfig.exportSuccess"),"success")}catch(P){V(o("config.agentConfig.exportFailed")+": "+P.message,"error")}};return be(()=>{U()}),(P,y)=>(d(),ne(Te,null,{title:J(()=>[e("h2",null,s(t(o)("config.agentConfig.title")),1)]),actions:J(()=>[e("button",{class:"action-btn",onClick:ie},[_(t(S),{icon:"carbon:upload"}),b(" "+s(t(o)("config.agentConfig.import")),1)]),e("button",{class:"action-btn",onClick:he,disabled:!c.value},[_(t(S),{icon:"carbon:download"}),b(" "+s(t(o)("config.agentConfig.export")),1)],8,ns)]),default:J(()=>{var K;return[e("div",as,[e("div",ls,[e("div",is,[e("h3",null,s(t(o)("config.agentConfig.configuredAgents")),1),e("span",cs,"("+s(m.length)+s(t(o)("config.agentConfig.agentCount"))+")",1)]),p.value?R("",!0):(d(),u("div",rs,[(d(!0),u(H,null,Y(m,l=>{var C,k;return d(),u("div",{key:l.id,class:oe(["agent-card",{active:((C=c.value)==null?void 0:C.id)===l.id}]),onClick:w=>h(l)},[e("div",us,[e("span",ps,s(l.name),1),_(t(S),{icon:"carbon:chevron-right"})]),e("p",fs,s(l.description),1),l.model?(d(),u("div",gs,[e("span",ms,s(l.model.type),1),e("span",hs,s(l.model.modelName),1)])):R("",!0),((k=l.availableTools)==null?void 0:k.length)>0?(d(),u("div",vs,[(d(!0),u(H,null,Y(l.availableTools.slice(0,3),w=>(d(),u("span",{key:w,class:"tool-tag"},s(X(w)),1))),128)),l.availableTools.length>3?(d(),u("span",ys," +"+s(l.availableTools.length-3),1)):R("",!0)])):R("",!0)],10,ds)}),128))])),p.value?(d(),u("div",_s,[_(t(S),{icon:"carbon:loading",class:"loading-icon"}),b(" "+s(t(o)("common.loading")),1)])):R("",!0),!p.value&&m.length===0?(d(),u("div",bs,[_(t(S),{icon:"carbon:bot",class:"empty-icon"}),e("p",null,s(t(o)("config.agentConfig.noAgent")),1)])):R("",!0),e("button",{class:"add-btn",onClick:i},[_(t(S),{icon:"carbon:add"}),b(" "+s(t(o)("config.agentConfig.createNew")),1)])]),c.value?(d(),u("div",Cs,[e("div",$s,[e("h3",null,s(c.value.name),1),e("div",ws,[e("button",{class:"action-btn primary",onClick:z},[_(t(S),{icon:"carbon:save"}),b(" "+s(t(o)("common.save")),1)]),e("button",{class:"action-btn danger",onClick:ce},[_(t(S),{icon:"carbon:trash-can"}),b(" "+s(t(o)("common.delete")),1)])])]),e("div",ks,[e("label",null,[b(s(t(o)("config.agentConfig.agentName"))+" ",1),y[15]||(y[15]=e("span",{class:"required"},"*",-1))]),q(e("input",{type:"text","onUpdate:modelValue":y[0]||(y[0]=l=>c.value.name=l),placeholder:t(o)("config.agentConfig.agentNamePlaceholder"),required:""},null,8,Ss),[[G,c.value.name]])]),e("div",Ts,[e("label",null,[b(s(t(o)("config.agentConfig.description"))+" ",1),y[16]||(y[16]=e("span",{class:"required"},"*",-1))]),q(e("textarea",{"onUpdate:modelValue":y[1]||(y[1]=l=>c.value.description=l),rows:"3",placeholder:t(o)("config.agentConfig.descriptionPlaceholder"),required:""},null,8,Ns),[[G,c.value.description]])]),e("div",Vs,[e("label",null,s(t(o)("config.agentConfig.nextStepPrompt")),1),e("textarea",{value:c.value.nextStepPrompt||"",onInput:y[2]||(y[2]=l=>c.value.nextStepPrompt=l.target.value),rows:"8",placeholder:t(o)("config.agentConfig.nextStepPromptPlaceholder")},null,40,As)]),e("div",Us,[e("h4",null,s(t(o)("config.agentConfig.modelConfiguration")),1),e("div",xs,[e("div",Ps,[e("button",{class:"model-btn",onClick:O,title:P.$t("model.switch")},[_(t(S),{icon:"carbon:build-run",width:"18"}),x.value?(d(),u("span",Ds,[e("span",Ms,s(x.value.type),1),y[17]||(y[17]=e("span",{class:"spacer"},null,-1)),e("span",Fs,s(x.value.modelName),1)])):(d(),u("span",Rs,[e("span",Ls,s(t(o)("config.agentConfig.modelConfigurationLabel")),1)])),_(t(S),{icon:A.value?"carbon:chevron-up":"carbon:chevron-down",width:"14",class:"chevron"},null,8,["icon"])],8,Es),A.value?(d(),u("div",{key:0,class:"model-dropdown",onClick:y[4]||(y[4]=_e(()=>{},["stop"]))},[e("div",Bs,[e("span",null,s(t(o)("config.agentConfig.modelConfigurationLabel")),1),e("button",{class:"close-btn",onClick:y[3]||(y[3]=l=>A.value=!1)},[_(t(S),{icon:"carbon:close",width:"16"})])]),e("div",Is,[(d(!0),u(H,null,Y(W,l=>{var C,k;return d(),u("button",{key:l.id,class:oe(["model-option",{active:((C=x.value)==null?void 0:C.id)===l.id}]),onClick:w=>N(l)},[e("span",Os,s(l.type),1),e("span",js,s(l.modelName),1),((k=x.value)==null?void 0:k.id)===l.id?(d(),ne(t(S),{key:0,icon:"carbon:checkmark",width:"16",class:"check-icon"})):R("",!0)],10,qs)}),128))])])):R("",!0),A.value?(d(),u("div",{key:1,class:"backdrop",onClick:y[5]||(y[5]=l=>A.value=!1)})):R("",!0)])])]),e("div",Gs,[e("h4",null,s(t(o)("config.agentConfig.toolConfiguration")),1),e("div",Js,[e("div",Ks,[e("span",null,s(t(o)("config.agentConfig.assignedTools"))+" ("+s((c.value.availableTools||[]).length)+")",1),$.length>0?(d(),u("button",{key:0,class:"action-btn small",onClick:r},[_(t(S),{icon:"carbon:add"}),b(" "+s(t(o)("config.agentConfig.addRemoveTools")),1)])):R("",!0)]),e("div",zs,[(d(!0),u(H,null,Y(c.value.availableTools||[],l=>(d(),u("div",{key:l,class:"tool-item assigned"},[e("div",Hs,[e("span",Ys,s(X(l)),1),e("span",Ws,s(se(l)),1)])]))),128)),c.value.availableTools.length===0?(d(),u("div",Xs,[_(t(S),{icon:"carbon:tool-box"}),e("span",null,s(t(o)("config.agentConfig.noAssignedTools")),1)])):R("",!0)])])])])):(d(),u("div",Qs,[_(t(S),{icon:"carbon:bot",class:"placeholder-icon"}),e("p",null,s(t(o)("config.agentConfig.selectAgentHint")),1)]))]),_(ue,{modelValue:T.value,"onUpdate:modelValue":y[9]||(y[9]=l=>T.value=l),title:t(o)("config.agentConfig.newAgent"),onConfirm:a},{default:J(()=>[e("div",Zs,[e("div",en,[e("label",null,[b(s(t(o)("config.agentConfig.agentName"))+" ",1),y[18]||(y[18]=e("span",{class:"required"},"*",-1))]),q(e("input",{type:"text","onUpdate:modelValue":y[6]||(y[6]=l=>F.name=l),placeholder:t(o)("config.agentConfig.agentNamePlaceholder"),required:""},null,8,on),[[G,F.name]])]),e("div",tn,[e("label",null,[b(s(t(o)("config.agentConfig.description"))+" ",1),y[19]||(y[19]=e("span",{class:"required"},"*",-1))]),q(e("textarea",{"onUpdate:modelValue":y[7]||(y[7]=l=>F.description=l),rows:"3",placeholder:t(o)("config.agentConfig.descriptionPlaceholder"),required:""},null,8,sn),[[G,F.description]])]),e("div",nn,[e("label",null,s(t(o)("config.agentConfig.nextStepPrompt")),1),e("textarea",{value:F.nextStepPrompt||"",onInput:y[8]||(y[8]=l=>F.nextStepPrompt=l.target.value),rows:"8",placeholder:t(o)("config.agentConfig.nextStepPromptPlaceholder")},null,40,an)])])]),_:1},8,["modelValue","title"]),_(ss,{modelValue:M.value,"onUpdate:modelValue":y[10]||(y[10]=l=>M.value=l),tools:$,"selected-tool-ids":((K=c.value)==null?void 0:K.availableTools)||[],onConfirm:D},null,8,["modelValue","tools","selected-tool-ids"]),_(ue,{modelValue:E.value,"onUpdate:modelValue":y[12]||(y[12]=l=>E.value=l),title:t(o)("config.agentConfig.deleteConfirm")},{footer:J(()=>[e("button",{class:"cancel-btn",onClick:y[11]||(y[11]=l=>E.value=!1)},s(t(o)("common.cancel")),1),e("button",{class:"confirm-btn danger",onClick:me},s(t(o)("common.delete")),1)]),default:J(()=>{var l;return[e("div",ln,[_(t(S),{icon:"carbon:warning",class:"warning-icon"}),e("p",null,[b(s(t(o)("config.agentConfig.deleteConfirmText"))+" ",1),e("strong",null,s((l=c.value)==null?void 0:l.name),1),b(" "+s(t(o)("common.confirm"))+"？ ",1)]),e("p",cn,s(t(o)("config.agentConfig.deleteWarning")),1)])]}),_:1},8,["modelValue","title"]),v.value?(d(),u("div",{key:0,class:"error-toast",onClick:y[13]||(y[13]=l=>v.value="")},[_(t(S),{icon:"carbon:error"}),b(" "+s(v.value),1)])):R("",!0),g.value?(d(),u("div",{key:1,class:"success-toast",onClick:y[14]||(y[14]=l=>g.value="")},[_(t(S),{icon:"carbon:checkmark"}),b(" "+s(g.value),1)])):R("",!0)]}),_:1}))}}),dn=le(rn,[["__scopeId","data-v-9e905dc6"]]),un={class:"custom-select"},pn=["title"],fn={key:0,class:"current-option"},gn={class:"option-name"},mn={key:1,class:"current-option"},hn={class:"dropdown-header"},vn={class:"select-options"},yn=["onClick"],_n={class:"option-name"},bn=ae({__name:"index",props:{modelValue:{},options:{},placeholder:{},dropdownTitle:{},icon:{},direction:{},dropStyles:{}},emits:["update:modelValue"],setup(I,{emit:o}){const n=I,p=o,v=L(!1),g=ee(()=>n.options.find(T=>T.id===n.modelValue)),m=T=>T.id===n.modelValue,c=()=>{v.value=!v.value},$=T=>{p("update:modelValue",T.id),v.value=!1};return(T,E)=>(d(),u("div",un,[e("button",{class:"select-btn",onClick:c,title:T.placeholder},[_(t(S),{icon:n.icon||"carbon:select-01",width:"18"},null,8,["icon"]),g.value?(d(),u("span",fn,[e("span",gn,s(g.value.name),1)])):(d(),u("span",mn,s(T.placeholder),1)),_(t(S),{icon:v.value?"carbon:chevron-up":"carbon:chevron-down",width:"14",class:"chevron"},null,8,["icon"])],8,pn),_(we,{name:"slideDown"},{default:J(()=>[q(e("div",{class:"select-dropdown",style:Ge({...T.dropStyles,...n.direction==="right"?{right:0}:{left:0}}),onClick:E[1]||(E[1]=_e(()=>{},["stop"]))},[e("div",hn,[e("span",null,s(T.dropdownTitle),1),e("button",{class:"close-btn",onClick:E[0]||(E[0]=M=>v.value=!1)},[_(t(S),{icon:"carbon:close",width:"16"})])]),e("div",vn,[(d(!0),u(H,null,Y(T.options,M=>(d(),u("button",{key:M.id,class:oe(["select-option",{active:m(M)}]),onClick:A=>$(M)},[e("span",_n,s(M.name),1),m(M)?(d(),ne(t(S),{key:0,icon:"carbon:checkmark",width:"16",class:"check-icon"})):R("",!0)],10,yn))),128))])],4),[[xe,v.value]])]),_:1}),v.value?(d(),u("div",{key:0,class:"backdrop",onClick:E[2]||(E[2]=M=>v.value=!1)})):R("",!0)]))}}),Ue=le(bn,[["__scopeId","data-v-138e90e7"]]),Cn=["disabled"],$n={class:"model-layout"},wn={class:"model-list"},kn={class:"list-header"},Sn={class:"model-count"},Tn={key:0,class:"models-container"},Nn=["onClick"],Vn={class:"model-card-header"},An={class:"model-name"},Un={class:"model-desc"},xn={key:0,class:"model-type"},Pn={class:"model-tag"},En={key:1,class:"loading-state"},Dn={key:2,class:"empty-state"},Mn={key:0,class:"model-detail"},Fn={class:"detail-header"},Rn={class:"detail-actions"},Ln={class:"form-item"},Bn={class:"form-item"},In=["placeholder"],qn={class:"form-item"},On=["placeholder"],jn={class:"form-item"},Gn=["placeholder"],Jn={class:"form-item"},Kn=["placeholder"],zn={class:"form-item"},Hn=["placeholder"],Yn={key:1,class:"no-selection"},Wn={class:"modal-form"},Xn={class:"form-item"},Qn={class:"form-item"},Zn=["placeholder"],ea={class:"form-item"},oa=["placeholder"],ta={class:"form-item"},sa=["placeholder"],na={class:"form-item"},aa=["placeholder"],la={class:"form-item"},ia=["placeholder"],ca={class:"delete-confirm"},ra={class:"warning-text"},da=ae({__name:"modelConfig",setup(I){const{t:o}=fe(),n=L(!1),p=L(""),v=L(""),g=te([]),m=te([]),c=L(null),$=L(!1),T=L(!1),E=ee({get(){var i;return(i=c.value)!=null&&i.headers?JSON.stringify(c.value.headers,null,2):""},set(i){c.value&&(c.value.headers=i.trim()?JSON.parse(i):null)}}),M=ee({get(){return A.headers?JSON.stringify(A.headers,null,2):""},set(i){A.headers=i.trim()?JSON.parse(i):null}}),A=te({baseUrl:"",headers:null,apiKey:"",modelName:"",modelDescription:"",type:""}),x=(i,a)=>{a==="success"?(v.value=i,setTimeout(()=>{v.value=""},3e3)):(p.value=i,setTimeout(()=>{p.value=""},5e3))},W=async()=>{n.value=!0;try{const[i,a]=await Promise.all([de.getAllModels(),de.getAllTypes()]),r=i.map(D=>({...D}));g.splice(0,g.length,...r),m.splice(0,m.length,...a),r.length>0&&await O(r[0])}catch(i){console.error("加载数据失败:",i),x(o("config.modelConfig.loadDataFailed")+": "+i.message,"error")}finally{n.value=!1}},O=async i=>{try{const a=await de.getModelById(i.id);c.value={...a}}catch(a){console.error("加载Model详情失败:",a),x(o("config.modelConfig.loadDetailsFailed")+": "+a.message,"error"),c.value={...i}}},N=()=>{A.baseUrl="",A.headers=null,A.apiKey="",A.modelName="",A.modelDescription="",A.type="",$.value=!0},F=async()=>{if(!A.modelName.trim()||!A.modelDescription.trim()){x(o("config.modelConfig.requiredFields"),"error");return}try{const i={baseUrl:A.baseUrl.trim(),headers:A.headers,apiKey:A.apiKey.trim(),modelName:A.modelName.trim(),modelDescription:A.modelDescription.trim(),type:A.type.trim()},a=await de.createModel(i);g.push(a),c.value=a,$.value=!1,x(o("config.modelConfig.createSuccess"),"success")}catch(i){x(o("config.modelConfig.createFailed")+": "+i.message,"error")}},X=async()=>{if(c.value){if(!c.value.modelName.trim()||!c.value.modelDescription.trim()){x(o("config.modelConfig.requiredFields"),"error");return}try{const i=await de.updateModel(c.value.id,c.value),a=g.findIndex(r=>r.id===i.id);a!==-1&&(g[a]=i),c.value=i,x(o("config.modelConfig.saveSuccess"),"success")}catch(i){x(o("config.modelConfig.saveFailed")+": "+i.message,"error")}}},se=()=>{T.value=!0},V=async()=>{if(c.value)try{await de.deleteModel(c.value.id);const i=g.findIndex(a=>a.id===c.value.id);i!==-1&&g.splice(i,1),c.value=g.length>0?g[0]:null,T.value=!1,x(o("config.modelConfig.deleteSuccess"),"success")}catch(i){x(o("config.modelConfig.deleteFailed")+": "+i.message,"error")}},U=()=>{const i=document.createElement("input");i.type="file",i.accept=".json",i.onchange=a=>{var D;const r=(D=a.target.files)==null?void 0:D[0];if(r){const z=new FileReader;z.onload=async ce=>{var me;try{const ie=JSON.parse((me=ce.target)==null?void 0:me.result);if(!ie.modelName||!ie.modelDescription)throw new Error(o("config.modelConfig.invalidFormat"));const{id:he,...P}=ie,y=await de.createModel(P);g.push(y),c.value=y,x(o("config.modelConfig.importSuccess"),"success")}catch(ie){x(o("config.modelConfig.importFailed")+": "+ie.message,"error")}},z.readAsText(r)}},i.click()},h=()=>{if(c.value)try{const i=JSON.stringify(c.value,null,2),a=new Blob([i],{type:"application/json"}),r=URL.createObjectURL(a),D=document.createElement("a");D.href=r,D.download=`model-${c.value.modelName}-${new Date().toISOString().split("T")[0]}.json`,D.click(),URL.revokeObjectURL(r),x(o("config.modelConfig.exportSuccess"),"success")}catch(i){x(o("config.modelConfig.exportFailed")+": "+i.message,"error")}};return be(()=>{W()}),(i,a)=>(d(),ne(Te,null,{title:J(()=>[e("h2",null,s(t(o)("config.modelConfig.title")),1)]),actions:J(()=>[e("button",{class:"action-btn",onClick:U},[_(t(S),{icon:"carbon:upload"}),b(" "+s(t(o)("config.modelConfig.import")),1)]),e("button",{class:"action-btn",onClick:h,disabled:!c.value},[_(t(S),{icon:"carbon:download"}),b(" "+s(t(o)("config.modelConfig.export")),1)],8,Cn)]),default:J(()=>[e("div",$n,[e("div",wn,[e("div",kn,[e("h3",null,s(t(o)("config.modelConfig.configuredModels")),1),e("span",Sn,"("+s(g.length)+")",1)]),n.value?R("",!0):(d(),u("div",Tn,[(d(!0),u(H,null,Y(g,r=>{var D;return d(),u("div",{key:r.id,class:oe(["model-card",{active:((D=c.value)==null?void 0:D.id)===r.id}]),onClick:z=>O(r)},[e("div",Vn,[e("span",An,s(r.modelName),1),_(t(S),{icon:"carbon:chevron-right"})]),e("p",Un,s(r.modelDescription),1),r.type?(d(),u("div",xn,[e("span",Pn,s(r.type),1)])):R("",!0)],10,Nn)}),128))])),n.value?(d(),u("div",En,[_(t(S),{icon:"carbon:loading",class:"loading-icon"}),b(" "+s(t(o)("common.loading")),1)])):R("",!0),!n.value&&g.length===0?(d(),u("div",Dn,[_(t(S),{icon:"carbon:bot",class:"empty-icon"}),e("p",null,s(t(o)("config.modelConfig.noModel")),1)])):R("",!0),e("button",{class:"add-btn",onClick:N},[_(t(S),{icon:"carbon:add"}),b(" "+s(t(o)("config.modelConfig.createNew")),1)])]),c.value?(d(),u("div",Mn,[e("div",Fn,[e("h3",null,s(c.value.modelName),1),e("div",Rn,[e("button",{class:"action-btn primary",onClick:X},[_(t(S),{icon:"carbon:save"}),b(" "+s(t(o)("common.save")),1)]),e("button",{class:"action-btn danger",onClick:se},[_(t(S),{icon:"carbon:trash-can"}),b(" "+s(t(o)("common.delete")),1)])])]),e("div",Ln,[e("label",null,[b(s(t(o)("config.modelConfig.type"))+" ",1),a[17]||(a[17]=e("span",{class:"required"},"*",-1))]),_(Ue,{modelValue:c.value.type,"onUpdate:modelValue":a[0]||(a[0]=r=>c.value.type=r),options:m.map(r=>({id:r,name:r})),placeholder:t(o)("config.modelConfig.typePlaceholder"),"dropdown-title":t(o)("config.modelConfig.typePlaceholder"),icon:"carbon:types"},null,8,["modelValue","options","placeholder","dropdown-title"])]),e("div",Bn,[e("label",null,[b(s(t(o)("config.modelConfig.baseUrl"))+" ",1),a[18]||(a[18]=e("span",{class:"required"},"*",-1))]),q(e("input",{type:"text","onUpdate:modelValue":a[1]||(a[1]=r=>c.value.baseUrl=r),placeholder:t(o)("config.modelConfig.baseUrlPlaceholder"),required:""},null,8,In),[[G,c.value.baseUrl]])]),e("div",qn,[e("label",null,s(t(o)("config.modelConfig.headers")),1),q(e("input",{type:"text","onUpdate:modelValue":a[2]||(a[2]=r=>E.value=r),placeholder:t(o)("config.modelConfig.headersPlaceholder")},null,8,On),[[G,E.value]])]),e("div",jn,[e("label",null,[b(s(t(o)("config.modelConfig.apiKey"))+" ",1),a[19]||(a[19]=e("span",{class:"required"},"*",-1))]),q(e("input",{type:"text","onUpdate:modelValue":a[3]||(a[3]=r=>c.value.apiKey=r),placeholder:t(o)("config.modelConfig.apiKeyPlaceholder"),required:""},null,8,Gn),[[G,c.value.apiKey]])]),e("div",Jn,[e("label",null,[b(s(t(o)("config.modelConfig.modelName"))+" ",1),a[20]||(a[20]=e("span",{class:"required"},"*",-1))]),q(e("input",{type:"text","onUpdate:modelValue":a[4]||(a[4]=r=>c.value.modelName=r),placeholder:t(o)("config.modelConfig.modelNamePlaceholder"),required:""},null,8,Kn),[[G,c.value.modelName]])]),e("div",zn,[e("label",null,[b(s(t(o)("config.modelConfig.description"))+" ",1),a[21]||(a[21]=e("span",{class:"required"},"*",-1))]),q(e("textarea",{"onUpdate:modelValue":a[5]||(a[5]=r=>c.value.modelDescription=r),rows:"3",placeholder:t(o)("config.modelConfig.descriptionPlaceholder"),required:""},null,8,Hn),[[G,c.value.modelDescription]])])])):(d(),u("div",Yn,[_(t(S),{icon:"carbon:bot",class:"placeholder-icon"}),e("p",null,s(t(o)("config.modelConfig.selectModelHint")),1)]))]),_(ue,{modelValue:$.value,"onUpdate:modelValue":a[12]||(a[12]=r=>$.value=r),title:t(o)("config.modelConfig.newModel"),onConfirm:F},{default:J(()=>[e("div",Wn,[e("div",Xn,[e("label",null,[b(s(t(o)("config.modelConfig.type"))+" ",1),a[22]||(a[22]=e("span",{class:"required"},"*",-1))]),_(Ue,{modelValue:A.type,"onUpdate:modelValue":a[6]||(a[6]=r=>A.type=r),options:m.map(r=>({id:r,name:r})),placeholder:t(o)("config.modelConfig.typePlaceholder"),"dropdown-title":t(o)("config.modelConfig.typePlaceholder"),icon:"carbon:types"},null,8,["modelValue","options","placeholder","dropdown-title"])]),e("div",Qn,[e("label",null,[b(s(t(o)("config.modelConfig.baseUrl"))+" ",1),a[23]||(a[23]=e("span",{class:"required"},"*",-1))]),q(e("input",{type:"text","onUpdate:modelValue":a[7]||(a[7]=r=>A.baseUrl=r),placeholder:t(o)("config.modelConfig.baseUrlPlaceholder"),required:""},null,8,Zn),[[G,A.baseUrl]])]),e("div",ea,[e("label",null,s(t(o)("config.modelConfig.headers")),1),q(e("input",{type:"text","onUpdate:modelValue":a[8]||(a[8]=r=>M.value=r),placeholder:t(o)("config.modelConfig.headersPlaceholder")},null,8,oa),[[G,M.value]])]),e("div",ta,[e("label",null,[b(s(t(o)("config.modelConfig.apiKey"))+" ",1),a[24]||(a[24]=e("span",{class:"required"},"*",-1))]),q(e("input",{type:"text","onUpdate:modelValue":a[9]||(a[9]=r=>A.apiKey=r),placeholder:t(o)("config.modelConfig.apiKeyPlaceholder"),required:""},null,8,sa),[[G,A.apiKey]])]),e("div",na,[e("label",null,[b(s(t(o)("config.modelConfig.modelName"))+" ",1),a[25]||(a[25]=e("span",{class:"required"},"*",-1))]),q(e("input",{type:"text","onUpdate:modelValue":a[10]||(a[10]=r=>A.modelName=r),placeholder:t(o)("config.modelConfig.modelNamePlaceholder"),required:""},null,8,aa),[[G,A.modelName]])]),e("div",la,[e("label",null,[b(s(t(o)("config.modelConfig.description"))+" ",1),a[26]||(a[26]=e("span",{class:"required"},"*",-1))]),q(e("textarea",{"onUpdate:modelValue":a[11]||(a[11]=r=>A.modelDescription=r),rows:"3",placeholder:t(o)("config.modelConfig.descriptionPlaceholder"),required:""},null,8,ia),[[G,A.modelDescription]])])])]),_:1},8,["modelValue","title"]),_(ue,{modelValue:T.value,"onUpdate:modelValue":a[14]||(a[14]=r=>T.value=r),title:"Delete confirmation"},{footer:J(()=>[e("button",{class:"cancel-btn",onClick:a[13]||(a[13]=r=>T.value=!1)},s(t(o)("common.cancel")),1),e("button",{class:"confirm-btn danger",onClick:V},s(t(o)("common.delete")),1)]),default:J(()=>{var r;return[e("div",ca,[_(t(S),{icon:"carbon:warning",class:"warning-icon"}),e("p",null,[b(s(t(o)("config.modelConfig.deleteConfirmText"))+" ",1),e("strong",null,s((r=c.value)==null?void 0:r.modelName),1),b(" "+s(t(o)("common.confirm"))+"？ ",1)]),e("p",ra,s(t(o)("config.modelConfig.deleteWarning")),1)])]}),_:1},8,["modelValue"]),p.value?(d(),u("div",{key:0,class:"error-toast",onClick:a[15]||(a[15]=r=>p.value="")},[_(t(S),{icon:"carbon:error"}),b(" "+s(p.value),1)])):R("",!0),v.value?(d(),u("div",{key:1,class:"success-toast",onClick:a[16]||(a[16]=r=>v.value="")},[_(t(S),{icon:"carbon:checkmark"}),b(" "+s(v.value),1)])):R("",!0)]),_:1}))}}),ua=le(da,[["__scopeId","data-v-dfd8f8e2"]]);class Se{static async getAllMcpServers(){const o=await fetch(`${this.BASE_URL}/list`);if(!o.ok)throw new Error(`Failed to get MCP server list: ${o.status}`);return await o.json()}static async addMcpServer(o){try{const n=await fetch(`${this.BASE_URL}/add`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(o)});if(!n.ok){const p=await new Response(n.body).text();throw new Error(`Failed to add MCP server: ${p}`)}return{success:!0,message:"Successfully added MCP server"}}catch(n){return console.error("Failed to add MCP server:",n),{success:!1,message:n instanceof Error?n.message:"Failed to add, please retry"}}}static async removeMcpServer(o){try{const n=await fetch(`${this.BASE_URL}/remove?id=${o}`);if(!n.ok)throw new Error(`Failed to delete MCP server: ${n.status}`);return{success:!0,message:"Successfully deleted MCP server"}}catch(n){return console.error(`Failed to delete MCP server[${o}]:`,n),{success:!1,message:n instanceof Error?n.message:"Failed to delete, please retry"}}}}ge(Se,"BASE_URL","/api/mcp");const pa={class:"mcp-config-panel"},fa={class:"mcp-header"},ga={class:"header-left"},ma={class:"mcp-stats"},ha={class:"stat-item"},va={class:"stat-label"},ya={class:"stat-value"},_a={class:"header-right"},ba={class:"search-box"},Ca=["placeholder"],$a={key:0,class:"loading-container"},wa={key:1,class:"mcp-layout"},ka={class:"mcp-table-container"},Sa={class:"section-title"},Ta={key:0,class:"empty-state"},Na={class:"empty-state-text"},Va={key:1,class:"mcp-table-wrapper"},Aa={class:"mcp-table"},Ua={class:"mcp-id"},xa={class:"mcp-server-name"},Pa={class:"server-name-content"},Ea={class:"mcp-connection-type"},Da={class:"mcp-config"},Ma=["title"],Fa={class:"mcp-actions"},Ra=["onClick","disabled"],La={class:"add-mcp-container"},Ba={class:"add-mcp-header"},Ia={class:"add-mcp-title"},qa={class:"mcp-form"},Oa={class:"mcp-form-group"},ja={class:"form-label"},Ga={class:"connection-type-options"},Ja={class:"connection-type-option"},Ka={for:"mcp-connection-type-studio",class:"radio-label"},za={class:"connection-type-desc"},Ha={class:"connection-type-option"},Ya={for:"mcp-connection-type-sse",class:"radio-label"},Wa={class:"connection-type-desc"},Xa={class:"connection-type-option"},Qa={for:"mcp-connection-type-streaming",class:"radio-label"},Za={class:"connection-type-desc"},el={class:"mcp-form-group"},ol={class:"form-label"},tl=["placeholder"],sl={class:"mcp-form-actions"},nl=["disabled"],al=["disabled"],ll={class:"mcp-form-instructions"},il={class:"indented-list"},cl=ae({__name:"mcpConfig",setup(I){const{t:o}=fe(),n=L(!1),p=L([]),v=L(""),g=te({connectionType:"STUDIO",configJson:""}),m=te({show:!1,text:"",type:"success"}),c=ee(()=>g.configJson.trim().length>0),$=ee(()=>{if(!v.value.trim())return p.value;const O=v.value.toLowerCase();return p.value.filter(N=>N.mcpServerName.toLowerCase().includes(O)||N.connectionType.toLowerCase().includes(O)||N.connectionConfig.toLowerCase().includes(O))}),T=O=>O?O.length>50?O.substring(0,50)+"...":O:"",E=(O,N="success")=>{m.text=O,m.type=N,m.show=!0,setTimeout(()=>{m.show=!1},3e3)},M=async()=>{try{n.value=!0,p.value=await Se.getAllMcpServers()}catch(O){console.error("加载MCP服务器列表失败:",O),E(o("config.basicConfig.loadConfigFailed"),"error")}finally{n.value=!1}},A=async()=>{if(!c.value){E(o("config.mcpConfig.configRequired"),"error");return}try{JSON.parse(g.configJson)}catch{E(o("config.mcpConfig.invalidJson"),"error");return}try{n.value=!0;const O={connectionType:g.connectionType,configJson:g.configJson},N=await Se.addMcpServer(O);N.success?(E(o("config.mcpConfig.addSuccess")),W(),await M()):E(N.message||o("config.mcpConfig.addFailed"),"error")}catch(O){console.error("添加MCP服务器失败:",O),E(o("config.mcpConfig.addFailed"),"error")}finally{n.value=!1}},x=async O=>{if(confirm(o("config.mcpConfig.deleteConfirm")))try{n.value=!0;const N=await Se.removeMcpServer(O);N.success?(E(o("config.mcpConfig.deleteSuccess")),await M()):E(N.message||o("config.mcpConfig.deleteFailed"),"error")}catch(N){console.error("删除MCP服务器失败:",N),E(o("config.mcpConfig.deleteFailed"),"error")}finally{n.value=!1}},W=()=>{g.connectionType="STUDIO",g.configJson=""};return be(()=>{M()}),(O,N)=>(d(),u("div",pa,[e("div",fa,[e("div",ga,[e("h2",null,s(t(o)("config.mcpConfig.title")),1),e("div",ma,[e("span",ha,[e("span",va,s(t(o)("config.mcpConfig.mcpServers"))+":",1),e("span",ya,s(p.value.length),1)])])]),e("div",_a,[e("div",ba,[q(e("input",{"onUpdate:modelValue":N[0]||(N[0]=F=>v.value=F),type:"text",placeholder:t(o)("config.mcpSearch"),class:"search-input"},null,8,Ca),[[G,v.value]]),N[5]||(N[5]=e("span",{class:"search-icon"},"🔍",-1))])])]),n.value?(d(),u("div",$a,[N[6]||(N[6]=e("div",{class:"loading-spinner"},null,-1)),e("p",null,s(t(o)("config.loading")),1)])):(d(),u("div",wa,[e("div",ka,[e("h3",Sa,s(t(o)("config.mcpConfig.serverList")),1),$.value.length===0?(d(),u("div",Ta,[N[7]||(N[7]=e("div",{class:"empty-state-icon"},"📂",-1)),e("div",Na,s(v.value?t(o)("config.notFound"):t(o)("config.mcpConfig.noServers")),1)])):(d(),u("div",Va,[e("table",Aa,[e("thead",null,[e("tr",null,[N[8]||(N[8]=e("th",null,"ID",-1)),e("th",null,s(t(o)("agent.name")),1),e("th",null,s(t(o)("config.mcpConfig.connectionType")),1),e("th",null,s(t(o)("config.mcpConfig.configJsonLabel")),1),e("th",null,s(t(o)("common.actions")),1)])]),e("tbody",null,[(d(!0),u(H,null,Y($.value,F=>(d(),u("tr",{key:F.id,class:"mcp-row"},[e("td",Ua,s(F.id),1),e("td",xa,[e("div",Pa,[N[9]||(N[9]=e("span",{class:"server-icon"},"🔌",-1)),b(" "+s(F.mcpServerName),1)])]),e("td",Ea,[e("span",{class:oe(["connection-type-badge",F.connectionType.toLowerCase()])},s(F.connectionType),3)]),e("td",Da,[e("div",{class:"config-preview",title:F.connectionConfig},s(T(F.connectionConfig)),9,Ma)]),e("td",Fa,[e("button",{onClick:X=>x(F.id),class:"action-btn delete-btn",disabled:n.value},s(t(o)("common.delete")),9,Ra)])]))),128))])])]))]),e("div",La,[e("div",Ba,[e("h3",Ia,s(t(o)("config.mcpConfig.addMcpServer")),1)]),e("div",qa,[e("div",Oa,[e("label",ja,s(t(o)("config.mcpConfig.connectionType"))+"：",1),e("div",Ga,[e("div",Ja,[q(e("input",{type:"radio",id:"mcp-connection-type-studio","onUpdate:modelValue":N[1]||(N[1]=F=>g.connectionType=F),value:"STUDIO"},null,512),[[Ae,g.connectionType]]),e("label",Ka,[N[10]||(N[10]=e("span",{class:"radio-title"},"STUDIO",-1)),e("span",za,s(t(o)("config.mcpConfig.instructionStep1LocalDesc")),1)])]),e("div",Ha,[q(e("input",{type:"radio",id:"mcp-connection-type-sse","onUpdate:modelValue":N[2]||(N[2]=F=>g.connectionType=F),value:"SSE"},null,512),[[Ae,g.connectionType]]),e("label",Ya,[N[11]||(N[11]=e("span",{class:"radio-title"},"SSE",-1)),e("span",Wa,s(t(o)("config.mcpConfig.instructionStep1RemoteDesc")),1)])]),e("div",Xa,[q(e("input",{type:"radio",id:"mcp-connection-type-streaming","onUpdate:modelValue":N[3]||(N[3]=F=>g.connectionType=F),value:"STREAMING"},null,512),[[Ae,g.connectionType]]),e("label",Qa,[N[12]||(N[12]=e("span",{class:"radio-title"},"Streamable HTTP",-1)),e("span",Za,s(t(o)("config.mcpConfig.instructionStep1RemoteDesc")),1)])])])]),e("div",el,[e("label",ol,s(t(o)("config.mcpConfig.configJsonLabel")),1),q(e("textarea",{"onUpdate:modelValue":N[4]||(N[4]=F=>g.configJson=F),placeholder:t(o)("config.mcpConfig.configJsonPlaceholder"),class:"config-textarea",rows:"6"},null,8,tl),[[G,g.configJson]])]),e("div",sl,[e("button",{onClick:A,class:"action-btn add-btn",disabled:n.value},s(t(o)("common.add")),9,nl),e("button",{onClick:W,class:"action-btn reset-btn",disabled:n.value},s(t(o)("common.reset")),9,al)])]),e("div",ll,[e("h4",null,s(t(o)("config.mcpConfig.instructions")),1),e("ol",null,[e("li",null,[b(s(t(o)("config.mcpConfig.instructionStep1"))+" ",1),e("ul",il,[e("li",null,[e("strong",null,s(t(o)("config.mcpConfig.instructionStep1Local")),1),b("："+s(t(o)("config.mcpConfig.instructionStep1LocalDesc")),1)]),e("li",null,[e("strong",null,s(t(o)("config.mcpConfig.instructionStep1Remote")),1),b("："+s(t(o)("config.mcpConfig.instructionStep1RemoteDesc")),1)])])]),e("li",null,s(t(o)("config.mcpConfig.instructionStep2")),1),e("li",null,s(t(o)("config.mcpConfig.instructionStep3")),1),e("li",null,s(t(o)("config.mcpConfig.instructionStep4")),1)])])])])),_(we,{name:"message-fade"},{default:J(()=>[m.show?(d(),u("div",{key:0,class:oe(["message-toast",m.type])},s(m.text),3)):R("",!0)]),_:1})]))}}),rl=le(cl,[["__scopeId","data-v-355da8bb"]]);class ve{static async handleResponse(o){if(!o.ok)try{const n=await o.json();throw new Error(n.message||`API request failed: ${o.status}`)}catch{throw new Error(`API request failed: ${o.status} ${o.statusText}`)}return o}static async getAllPrompts(o){try{const n=await fetch(`${this.BASE_URL}/namespace/${o}`);return await(await this.handleResponse(n)).json()}catch(n){throw console.error("Failed to get Prompt list:",n),n}}static async getPromptById(o){try{const n=await fetch(`${this.BASE_URL}/${o}`);return await(await this.handleResponse(n)).json()}catch(n){throw console.error(`Failed to get Pr'o'm'p't[${o}] details:`,n),n}}static async createPrompt(o){try{const n=await fetch(this.BASE_URL,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(o)});return await(await this.handleResponse(n)).json()}catch(n){throw console.error("Failed to create Prompt:",n),n}}static async updatePrompt(o,n){try{const p=await fetch(`${this.BASE_URL}/${o}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(n)});return await(await this.handleResponse(p)).json()}catch(p){throw console.error(`Failed to update Prompt[${o}]:`,p),p}}static async deletePrompt(o){try{const n=await fetch(`${this.BASE_URL}/${o}`,{method:"DELETE"});if(n.status===400)throw new Error("Cannot delete default Prompt");await this.handleResponse(n)}catch(n){throw console.error(`Failed to delete Prompt[${o}]:`,n),n}}}ge(ve,"BASE_URL","/api/prompt");const dl={class:"custom-select"},ul=["title"],pl={key:0,class:"current-option"},fl={class:"option-name"},gl={key:1,class:"current-option"},ml={class:"select-options"},hl=["onClick"],vl={class:"option-name"},yl=ae({__name:"select",props:{modelValue:{},options:{},placeholder:{}},emits:["update:modelValue"],setup(I,{emit:o}){const n=I,p=o,v=L(!1),g=ee(()=>n.options.find(T=>T.id===n.modelValue)),m=T=>T.id===n.modelValue,c=()=>{v.value=!v.value},$=T=>{p("update:modelValue",T.id),v.value=!1};return(T,E)=>(d(),u("div",dl,[e("button",{class:"select-btn",onClick:c,title:T.placeholder},[g.value?(d(),u("span",pl,[e("span",fl,s(g.value.name),1)])):(d(),u("span",gl,s(T.placeholder),1)),_(t(S),{icon:v.value?"carbon:chevron-up":"carbon:chevron-down",width:"14",class:"chevron"},null,8,["icon"])],8,ul),_(we,{name:"slideDown"},{default:J(()=>[q(e("div",{class:"select-dropdown",onClick:E[0]||(E[0]=_e(()=>{},["stop"]))},[e("div",ml,[(d(!0),u(H,null,Y(T.options,M=>(d(),u("button",{key:M.id,class:oe(["select-option",{active:m(M)}]),onClick:A=>$(M)},[e("span",vl,s(M.name),1),m(M)?(d(),ne(t(S),{key:0,icon:"carbon:checkmark",width:"16",class:"check-icon"})):R("",!0)],10,hl))),128))])],512),[[xe,v.value]])]),_:1}),v.value?(d(),u("div",{key:0,class:"backdrop",onClick:E[1]||(E[1]=M=>v.value=!1)})):R("",!0)]))}}),ke=le(yl,[["__scopeId","data-v-4c6d676f"]]),_l={class:"prompt-layout"},bl={class:"prompt-list"},Cl={class:"list-header"},$l={class:"prompt-count"},wl={key:0,class:"prompts-container"},kl=["onClick"],Sl={class:"prompt-card-header"},Tl={class:"prompt-name"},Nl={key:0,class:"prompt-desc"},Vl={class:"tags"},Al={class:"tag"},Ul={key:1,class:"loading-state"},xl={key:2,class:"empty-state"},Pl={key:0,class:"prompt-detail"},El={class:"detail-header"},Dl={class:"detail-actions"},Ml={class:"form-item"},Fl=["placeholder"],Rl={class:"form-item"},Ll={class:"form-item"},Bl={class:"form-item"},Il=["placeholder"],ql={class:"form-item"},Ol=["placeholder"],jl={key:1,class:"no-selection"},Gl={class:"modal-form"},Jl={class:"form-item"},Kl=["placeholder"],zl={class:"form-item"},Hl={class:"form-item"},Yl={class:"form-item"},Wl=["placeholder"],Xl={class:"form-item"},Ql=["placeholder"],Zl={class:"delete-confirm"},ei={class:"warning-text"},oi=ae({__name:"dynamicPromptConfig",setup(I){const{t:o}=fe(),{success:n,error:p}=Me(),v=Pe(),g=L(!1),m=te([]),c=L(null),$=L(!1),T=L(!1),E=[{id:"USER",name:"USER"},{id:"ASSISTANT",name:"ASSISTANT"},{id:"SYSTEM",name:"SYSTEM"},{id:"TOOL",name:"TOOL"}],M=[{id:"LLM",name:"LLM"},{id:"PLANNING",name:"PLANNING"},{id:"AGENT",name:"AGENT"}],A={promptName:"",messageType:"",type:"",promptDescription:""},x=te({...A}),W=async()=>{g.value=!0;try{const i=await ve.getAllPrompts(v.namespace);i.length>0&&await O(i[0]),m.splice(0,m.length,...i)}catch(i){console.error("加载数据失败:",i),p(o("config.promptConfig.loadDataFailed")+": "+i.message)}finally{g.value=!1}},O=async i=>{try{const a=await ve.getPromptById(i.id);c.value={...a}}catch(a){console.error("加载Prompt详情失败:",a),p(o("config.promptConfig.loadDetailsFailed")+": "+a.message),c.value={...i}}},N=async()=>{if(se(x))try{const i={...x,builtIn:!1},a=await ve.createPrompt(i);m.push(a),c.value=a,$.value=!1,n(o("config.promptConfig.createSuccess"))}catch(i){p(o("config.promptConfig.createFailed")+": "+i.message)}},F=async()=>{if(c.value&&se(c.value))try{const i=await ve.updatePrompt(c.value.id,c.value),a=m.findIndex(r=>r.id===i.id);a!==-1&&(m[a]=i),c.value={...i},n(o("config.promptConfig.saveSuccess"))}catch(i){p(o("config.promptConfig.saveFailed")+": "+i.message)}},X=async()=>{if(c.value)try{await ve.deletePrompt(c.value.id);const i=m.findIndex(a=>a.id===c.value.id);i!==-1&&m.splice(i,1),c.value=m.length>0?m[0]:null,T.value=!1,n(o("config.promptConfig.deleteSuccess"))}catch(i){p(o("config.promptConfig.deleteFailed")+": "+i.message)}};function se(i){const a=["promptName","messageType","type","promptContent"];for(const D of a){const z=i[D];if(z==null||typeof z=="string"&&z.trim()==="")return p(`${D} is required and cannot be empty`),!1}return!!V(m,i)}function V(i,a){const r=i.find(D=>D.promptName===a.promptName);return r&&a.id&&r.id!==a.id?(p(`Name "${a.promptName}" already exists. Please use a different name.`),!1):r&&!a.id?(p(`Name "${a.promptName}" already exists. Please use a different name.`),!1):!0}const U=()=>{x.promptName="",x.promptDescription="",x.promptContent="",Object.assign(x,A),$.value=!0},h=()=>{T.value=!0};return be(()=>{W()}),(i,a)=>(d(),ne(Te,null,{title:J(()=>[e("h2",null,s(t(o)("config.promptConfig.title")),1)]),default:J(()=>[e("div",_l,[e("div",bl,[e("div",Cl,[e("h3",null,s(t(o)("config.promptConfig.configuredprompts")),1),e("span",$l,"("+s(m.length)+s(t(o)("config.promptConfig.promptCount"))+")",1)]),g.value?R("",!0):(d(),u("div",wl,[(d(!0),u(H,null,Y(m,r=>{var D,z;return d(),u("div",{key:r.id,class:oe(["prompt-card",{active:((D=c.value)==null?void 0:D.id)===r.id}]),onClick:ce=>O(r)},[e("div",Sl,[e("span",Tl,s(r.promptName),1),_(t(S),{icon:"carbon:chevron-right"})]),(z=r.promptDescription)!=null&&z.trim()?(d(),u("p",Nl,s(r.promptDescription),1)):R("",!0),e("div",Vl,[e("span",Al,s(r.builtIn?t(o)("config.promptConfig.builtIn"):t(o)("config.promptConfig.custom")),1)])],10,kl)}),128))])),g.value?(d(),u("div",Ul,[_(t(S),{icon:"carbon:loading",class:"loading-icon"}),b(" "+s(t(o)("common.loading")),1)])):R("",!0),!g.value&&m.length===0?(d(),u("div",xl,[_(t(S),{icon:"carbon:bot",class:"empty-icon"}),e("p",null,s(t(o)("config.promptConfig.noPrompts")),1)])):R("",!0),e("button",{class:"add-btn",onClick:U},[_(t(S),{icon:"carbon:add"}),b(" "+s(t(o)("config.promptConfig.createNew")),1)])]),c.value?(d(),u("div",Pl,[e("div",El,[e("h3",null,s(c.value.promptName),1),e("div",Dl,[e("button",{class:"action-btn primary",onClick:F},[_(t(S),{icon:"carbon:save"}),b(" "+s(t(o)("common.save")),1)]),c.value.builtIn?R("",!0):(d(),u("button",{key:0,class:"action-btn danger",onClick:h},[_(t(S),{icon:"carbon:trash-can"}),b(" "+s(t(o)("common.delete")),1)]))])]),e("div",Ml,[e("label",null,[b(s(t(o)("config.promptConfig.promptName"))+" ",1),a[15]||(a[15]=e("span",{class:"required"},"*",-1))]),q(e("input",{disabled:!0,type:"text","onUpdate:modelValue":a[0]||(a[0]=r=>c.value.promptName=r),placeholder:t(o)("config.promptConfig.placeholder"),required:""},null,8,Fl),[[G,c.value.promptName]])]),e("div",Rl,[e("label",null,[b(s(t(o)("config.promptConfig.messageType"))+" ",1),a[16]||(a[16]=e("span",{class:"required"},"*",-1))]),c.value.builtIn?q((d(),u("input",{key:1,type:"text","onUpdate:modelValue":a[2]||(a[2]=r=>c.value.messageType=r),disabled:!0},null,512)),[[G,c.value.messageType]]):(d(),ne(ke,{key:0,modelValue:c.value.messageType,"onUpdate:modelValue":a[1]||(a[1]=r=>c.value.messageType=r),options:E,disabled:c.value.builtIn,placeholder:t(o)("config.promptConfig.placeholder")},null,8,["modelValue","disabled","placeholder"]))]),e("div",Ll,[e("label",null,[b(s(t(o)("config.promptConfig.type"))+" ",1),a[17]||(a[17]=e("span",{class:"required"},"*",-1))]),c.value.builtIn?q((d(),u("input",{key:1,type:"text","onUpdate:modelValue":a[4]||(a[4]=r=>c.value.type=r),disabled:!0,required:""},null,512)),[[G,c.value.type]]):(d(),ne(ke,{key:0,modelValue:c.value.type,"onUpdate:modelValue":a[3]||(a[3]=r=>c.value.type=r),options:M,disabled:c.value.builtIn,placeholder:t(o)("config.promptConfig.placeholder")},null,8,["modelValue","disabled","placeholder"]))]),e("div",Bl,[e("label",null,s(t(o)("config.promptConfig.description")),1),q(e("textarea",{"onUpdate:modelValue":a[5]||(a[5]=r=>c.value.promptDescription=r),rows:"2",placeholder:t(o)("config.promptConfig.descriptionPlaceholder"),required:""},null,8,Il),[[G,c.value.promptDescription]])]),e("div",ql,[e("label",null,[b(s(t(o)("config.promptConfig.promptContent"))+" ",1),a[18]||(a[18]=e("span",{class:"required"},"*",-1))]),q(e("textarea",{"onUpdate:modelValue":a[6]||(a[6]=r=>c.value.promptContent=r),rows:"8",placeholder:t(o)("config.promptConfig.promptContentPlaceholder"),required:""},null,8,Ol),[[G,c.value.promptContent]])])])):(d(),u("div",jl,[_(t(S),{icon:"carbon:bot",class:"placeholder-icon"}),e("p",null,s(t(o)("config.promptConfig.selectPromptHint")),1)]))]),_(ue,{modelValue:$.value,"onUpdate:modelValue":a[12]||(a[12]=r=>$.value=r),title:t(o)("config.promptConfig.newPrompt"),onConfirm:N},{default:J(()=>[e("div",Gl,[e("div",Jl,[e("label",null,[b(s(t(o)("config.promptConfig.promptName"))+" ",1),a[19]||(a[19]=e("span",{class:"required"},"*",-1))]),q(e("input",{type:"text","onUpdate:modelValue":a[7]||(a[7]=r=>x.promptName=r),placeholder:t(o)("config.promptConfig.placeholder"),required:""},null,8,Kl),[[G,x.promptName]])]),e("div",zl,[e("label",null,[b(s(t(o)("config.promptConfig.messageType"))+" ",1),a[20]||(a[20]=e("span",{class:"required"},"*",-1))]),_(ke,{modelValue:x.messageType,"onUpdate:modelValue":a[8]||(a[8]=r=>x.messageType=r),options:E,placeholder:t(o)("config.promptConfig.placeholder")},null,8,["modelValue","placeholder"])]),e("div",Hl,[e("label",null,[b(s(t(o)("config.promptConfig.type"))+" ",1),a[21]||(a[21]=e("span",{class:"required"},"*",-1))]),_(ke,{modelValue:x.type,"onUpdate:modelValue":a[9]||(a[9]=r=>x.type=r),options:M,placeholder:t(o)("config.promptConfig.placeholder")},null,8,["modelValue","placeholder"])]),e("div",Yl,[e("label",null,s(t(o)("config.promptConfig.description")),1),q(e("textarea",{"onUpdate:modelValue":a[10]||(a[10]=r=>x.promptDescription=r),rows:"3",placeholder:t(o)("config.promptConfig.descriptionPlaceholder")},null,8,Wl),[[G,x.promptDescription]])]),e("div",Xl,[e("label",null,[b(s(t(o)("config.promptConfig.promptContent"))+" ",1),a[22]||(a[22]=e("span",{class:"required"},"*",-1))]),q(e("textarea",{"onUpdate:modelValue":a[11]||(a[11]=r=>x.promptContent=r),rows:"8",placeholder:t(o)("config.promptConfig.promptContentPlaceholder"),required:""},null,8,Ql),[[G,x.promptContent]])])])]),_:1},8,["modelValue","title"]),_(ue,{modelValue:T.value,"onUpdate:modelValue":a[14]||(a[14]=r=>T.value=r),title:t(o)("config.promptConfig.deleteConfirm")},{footer:J(()=>[e("button",{class:"cancel-btn",onClick:a[13]||(a[13]=r=>T.value=!1)},s(t(o)("common.cancel")),1),e("button",{class:"confirm-btn danger",onClick:X},s(t(o)("common.delete")),1)]),default:J(()=>{var r;return[e("div",Zl,[_(t(S),{icon:"carbon:warning",class:"warning-icon"}),e("p",null,[b(s(t(o)("config.promptConfig.deleteConfirmText"))+" ",1),e("strong",null,s((r=c.value)==null?void 0:r.promptName),1),b(" "+s(t(o)("common.confirm"))+"？ ",1)]),e("p",ei,s(t(o)("config.promptConfig.deleteWarning")),1)])]}),_:1},8,["modelValue","title"])]),_:1}))}}),ti=le(oi,[["__scopeId","data-v-697ad7ba"]]);class ye{static async handleResponse(o){if(!o.ok)try{const n=await o.json();throw new Error(n.message||`API request failed: ${o.status}`)}catch{throw new Error(`API request failed: ${o.status} ${o.statusText}`)}return o}static async getAllNamespaces(){try{const o=await fetch(`${this.BASE_URL}`);return await(await this.handleResponse(o)).json()}catch(o){throw console.error("Failed to get Namespace list:",o),o}}static async getNamespaceById(o){try{const n=await fetch(`${this.BASE_URL}/${o}`);return await(await this.handleResponse(n)).json()}catch(n){throw console.error(`Failed to get Namespace[${o}] details:`,n),n}}static async createNamespace(o){try{const n=await fetch(this.BASE_URL,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(o)});return await(await this.handleResponse(n)).json()}catch(n){throw console.error("Failed to create Namespace:",n),n}}static async updateNamespace(o,n){try{const p=await fetch(`${this.BASE_URL}/${o}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(n)});return await(await this.handleResponse(p)).json()}catch(p){throw console.error(`Failed to update Namespace[${o}]:`,p),p}}static async deleteNamespace(o){try{const n=await fetch(`${this.BASE_URL}/${o}`,{method:"DELETE"});if(n.status===400)throw new Error("Cannot delete default Namespace");await this.handleResponse(n)}catch(n){throw console.error(`Failed to delete Namespace[${o}]:`,n),n}}}ge(ye,"BASE_URL","/api/namespace");const si={class:"namespace-layout"},ni={class:"namespace-list"},ai={class:"list-header"},li={key:0,class:"namespaces-container"},ii=["onClick"],ci={class:"namespace-card-header"},ri={class:"namespace-name"},di={key:0,class:"loading-state"},ui={key:1,class:"empty-state"},pi={key:0,class:"namespace-detail"},fi={class:"detail-header"},gi={class:"detail-actions"},mi={class:"form-item"},hi=["placeholder"],vi={key:1,class:"no-selection"},yi={class:"modal-form"},_i={class:"form-item"},bi=["placeholder"],Ci={class:"delete-confirm"},$i={class:"warning-text"},wi=ae({__name:"namespaceConfig",setup(I){const{t:o}=fe(),{success:n,error:p}=Me(),v=L(!1),g=te([]),m=L(null),c=L(!1),$=L(!1),T={name:""},E=te({...T}),M=async()=>{v.value=!0;try{const V=await ye.getAllNamespaces();V.length>0&&await A(V[0]),g.splice(0,g.length,...V)}catch(V){console.error("加载数据失败:",V),p(o("config.namespaceConfig.loadDataFailed")+": "+V.message)}finally{v.value=!1}},A=async V=>{try{const U=await ye.getNamespaceById(V.id);m.value={...U}}catch(U){console.error("加载Prompt详情失败:",U),p(o("config.namespaceConfig.loadDetailsFailed")+": "+U.message),m.value={...V}}},x=async()=>{if(N(E))try{const V={...E},U=await ye.createNamespace(V);g.push(U),m.value=U,c.value=!1,n(o("config.namespaceConfig.createSuccess"))}catch(V){p(o("config.namespaceConfig.createFailed")+": "+V.message)}},W=async()=>{if(m.value&&N(m.value))try{const V=await ye.updateNamespace(m.value.id,m.value),U=g.findIndex(h=>h.id===V.id);U!==-1&&(g[U]=V),m.value={...V},n(o("config.namespaceConfig.saveSuccess"))}catch(V){p(o("config.namespaceConfig.saveFailed")+": "+V.message)}},O=async()=>{if(m.value)try{await ye.deleteNamespace(m.value.id);const V=g.findIndex(U=>U.id===m.value.id);V!==-1&&g.splice(V,1),m.value=g.length>0?g[0]:null,$.value=!1,n(o("config.namespaceConfig.deleteSuccess"))}catch(V){p(o("config.namespaceConfig.deleteFailed")+": "+V.message)}};function N(V){const U=["name"];for(const i of U){const a=V[i];if(a==null||typeof a=="string"&&a.trim()==="")return p(`${i} is required and cannot be empty`),!1}return!!F(g,V)}function F(V,U){const h=V.find(i=>i.name===U.name);return h&&U.id&&h.id!==U.id?(p(`Name "${U.name}" already exists. Please use a different name.`),!1):h&&!U.id?(p(`Name "${U.name}" already exists. Please use a different name.`),!1):!0}const X=()=>{E.name="",Object.assign(E,T),c.value=!0},se=()=>{$.value=!0};return be(()=>{M()}),(V,U)=>(d(),ne(Te,null,{title:J(()=>[e("h2",null,s(t(o)("config.namespaceConfig.title")),1)]),default:J(()=>[e("div",si,[e("div",ni,[e("div",ai,[e("h3",null,s(t(o)("config.namespaceConfig.configured")),1)]),v.value?R("",!0):(d(),u("div",li,[(d(!0),u(H,null,Y(g,h=>{var i;return d(),u("div",{key:h.id,class:oe(["namespace-card",{active:((i=m.value)==null?void 0:i.id)===h.id}]),onClick:a=>A(h)},[e("div",ci,[e("span",ri,s(h.name),1),_(t(S),{icon:"carbon:chevron-right"})])],10,ii)}),128))]))]),v.value?(d(),u("div",di,[_(t(S),{icon:"carbon:loading",class:"loading-icon"}),b(" "+s(t(o)("common.loading")),1)])):R("",!0),!v.value&&g.length===0?(d(),u("div",ui,[_(t(S),{icon:"carbon:bot",class:"empty-icon"}),e("p",null,s(t(o)("config.namespaceConfig.noPrompts")),1)])):R("",!0),e("button",{class:"add-btn",onClick:X},[_(t(S),{icon:"carbon:add"}),b(" "+s(t(o)("config.namespaceConfig.createNew")),1)])]),m.value?(d(),u("div",pi,[e("div",fi,[e("h3",null,s(m.value.name),1),e("div",gi,[e("button",{class:"action-btn primary",onClick:W},[_(t(S),{icon:"carbon:save"}),b(" "+s(t(o)("common.save")),1)]),e("button",{class:"action-btn danger",onClick:se},[_(t(S),{icon:"carbon:trash-can"}),b(" "+s(t(o)("common.delete")),1)])])]),e("div",mi,[e("label",null,[b(s(t(o)("config.namespaceConfig.name"))+" ",1),U[5]||(U[5]=e("span",{class:"required"},"*",-1))]),q(e("input",{disabled:!0,type:"text","onUpdate:modelValue":U[0]||(U[0]=h=>m.value.name=h),placeholder:t(o)("config.namespaceConfig.placeholder"),required:""},null,8,hi),[[G,m.value.name]])])])):(d(),u("div",vi,[_(t(S),{icon:"carbon:bot",class:"placeholder-icon"}),e("p",null,s(t(o)("config.namespaceConfig.selectPromptHint")),1)])),_(ue,{modelValue:c.value,"onUpdate:modelValue":U[2]||(U[2]=h=>c.value=h),title:t(o)("config.namespaceConfig.newPrompt"),onConfirm:x},{default:J(()=>[e("div",yi,[e("div",_i,[e("label",null,[b(s(t(o)("config.namespaceConfig.name"))+" ",1),U[6]||(U[6]=e("span",{class:"required"},"*",-1))]),q(e("input",{type:"text","onUpdate:modelValue":U[1]||(U[1]=h=>E.name=h),placeholder:t(o)("config.namespaceConfig.placeholder"),required:""},null,8,bi),[[G,E.name]])])])]),_:1},8,["modelValue","title"]),_(ue,{modelValue:$.value,"onUpdate:modelValue":U[4]||(U[4]=h=>$.value=h),title:t(o)("config.namespaceConfig.deleteConfirm")},{footer:J(()=>[e("button",{class:"cancel-btn",onClick:U[3]||(U[3]=h=>$.value=!1)},s(t(o)("common.cancel")),1),e("button",{class:"confirm-btn danger",onClick:O},s(t(o)("common.delete")),1)]),default:J(()=>{var h;return[e("div",Ci,[_(t(S),{icon:"carbon:warning",class:"warning-icon"}),e("p",null,[b(s(t(o)("config.namespaceConfig.deleteConfirmText"))+" ",1),e("strong",null,s((h=m.value)==null?void 0:h.name),1),b(" "+s(t(o)("common.confirm"))+"？ ",1)]),e("p",$i,s(t(o)("config.namespaceConfig.deleteWarning")),1)])]}),_:1},8,["modelValue","title"])]),_:1}))}}),ki=le(wi,[["__scopeId","data-v-d0b2912a"]]),Si=ae({__name:"namespaceSwitch",setup(I){const{t:o}=fe(),{namespace:n,setNamespace:p,db:v}=Pe(),g=m=>{p(m)};return(m,c)=>(d(),u("div",null,[_(Ue,{modelValue:t(n),"onUpdate:modelValue":c[0]||(c[0]=$=>Je(n)?n.value=$:null),options:t(v),"dropdown-title":t(o)("config.promptConfig.namespace"),onChange:g,placeholder:t(o)("config.promptConfig.selectNamespace"),direction:"right",dropStyles:{minWidth:"180px"}},null,8,["modelValue","options","dropdown-title","placeholder"])]))}}),Ti={class:"config-container"},Ni={class:"config-header"},Vi={class:"header-actions"},Ai={class:"header-actions-left"},Ui={class:"header-actions-right"},xi={class:"config-content"},Pi={class:"config-nav"},Ei=["onClick"],Di={class:"config-details"},Mi=ae({__name:"index",setup(I){const{t:o}=fe(),n=Ke(),p=He(),v=L(n.params.category||"basic"),g={basic:De,agent:dn,model:ua,mcp:rl,prompt:ti,namespace:ki},m=ee(()=>{const T=v.value;return g[T]||De}),c=ee(()=>[{key:"basic",label:o("config.categories.basic"),icon:"carbon:settings"},{key:"agent",label:o("config.categories.agent"),icon:"carbon:bot"},{key:"model",label:o("config.categories.model"),icon:"carbon:build-image"},{key:"mcp",label:o("config.categories.mcp"),icon:"carbon:tool-box"},{key:"prompt",label:o("config.categories.prompt"),icon:"carbon:repo-artifact"},{key:"namespace",disabled:!0,icon:"carbon:repo-artifact"}]),$=T=>{v.value=T,p.push({name:n.name,params:{...n.params,category:T},query:n.query})};return(T,E)=>(d(),u("div",Ti,[e("div",Ni,[e("div",Vi,[e("div",Ai,[e("button",{class:"action-btn",onClick:E[0]||(E[0]=M=>T.$router.push("/"))},[_(t(S),{icon:"carbon:arrow-left"}),b(" "+s(T.$t("backHome")),1)]),_(Ye)]),e("div",Ui,[_(Si)])])]),e("div",xi,[e("nav",Pi,[(d(!0),u(H,null,Y(c.value,(M,A)=>(d(),u(H,null,[M.disabled?R("",!0):(d(),u("div",{key:A,class:oe(["nav-item",{active:v.value===M.key}]),onClick:x=>$(M.key)},[_(t(S),{icon:M.icon,width:"20"},null,8,["icon"]),e("span",null,s(M.label),1)],10,Ei))],64))),256))]),e("div",Di,[(d(),ne(ze(m.value)))])])]))}}),qi=le(Mi,[["__scopeId","data-v-5d4a51b7"]]);export{qi as default};
