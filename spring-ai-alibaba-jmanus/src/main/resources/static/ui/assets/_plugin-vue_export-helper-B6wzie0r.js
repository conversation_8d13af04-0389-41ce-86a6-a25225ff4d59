<<<<<<<< HEAD:spring-ai-alibaba-jmanus/src/main/resources/static/ui/assets/_plugin-vue_export-helper-rrmgtdBH.js
import{d as mt,N as G}from"./index-CIc5WN-g.js";const st=/^[a-z0-9]+(-[a-z0-9]+)*$/,M=(t,n,o,i="")=>{const e=t.split(":");if(t.slice(0,1)==="@"){if(e.length<2||e.length>3)return null;i=e.shift().slice(1)}if(e.length>3||!e.length)return null;if(e.length>1){const c=e.pop(),l=e.pop(),f={provider:e.length>0?e[0]:i,prefix:l,name:c};return n&&!L(f)?null:f}const s=e[0],r=s.split("-");if(r.length>1){const c={provider:i,prefix:r.shift(),name:r.join("-")};return n&&!L(c)?null:c}if(o&&i===""){const c={provider:i,prefix:"",name:s};return n&&!L(c,o)?null:c}return null},L=(t,n)=>t?!!((n&&t.prefix===""||t.prefix)&&t.name):!1,rt=Object.freeze({left:0,top:0,width:16,height:16}),A=Object.freeze({rotate:0,vFlip:!1,hFlip:!1}),_=Object.freeze({...rt,...A}),z=Object.freeze({..._,body:"",hidden:!1});function yt(t,n){const o={};!t.hFlip!=!n.hFlip&&(o.hFlip=!0),!t.vFlip!=!n.vFlip&&(o.vFlip=!0);const i=((t.rotate||0)+(n.rotate||0))%4;return i&&(o.rotate=i),o}function B(t,n){const o=yt(t,n);for(const i in z)i in A?i in t&&!(i in o)&&(o[i]=A[i]):i in n?o[i]=n[i]:i in t&&(o[i]=t[i]);return o}function bt(t,n){const o=t.icons,i=t.aliases||Object.create(null),e=Object.create(null);function s(r){if(o[r])return e[r]=[];if(!(r in e)){e[r]=null;const c=i[r]&&i[r].parent,l=c&&s(c);l&&(e[r]=[c].concat(l))}return e[r]}return Object.keys(o).concat(Object.keys(i)).forEach(s),e}function xt(t,n,o){const i=t.icons,e=t.aliases||Object.create(null);let s={};function r(c){s=B(i[c]||e[c],s)}return r(n),o.forEach(r),B(t,s)}function ct(t,n){const o=[];if(typeof t!="object"||typeof t.icons!="object")return o;t.not_found instanceof Array&&t.not_found.forEach(e=>{n(e,null),o.push(e)});const i=bt(t);for(const e in i){const s=i[e];s&&(n(e,xt(t,e,s)),o.push(e))}return o}const It={provider:"",aliases:{},not_found:{},...rt};function D(t,n){for(const o in n)if(o in t&&typeof t[o]!=typeof n[o])return!1;return!0}function lt(t){if(typeof t!="object"||t===null)return null;const n=t;if(typeof n.prefix!="string"||!t.icons||typeof t.icons!="object"||!D(t,It))return null;const o=n.icons;for(const e in o){const s=o[e];if(!e||typeof s.body!="string"||!D(s,z))return null}const i=n.aliases||Object.create(null);for(const e in i){const s=i[e],r=s.parent;if(!e||typeof r!="string"||!o[r]&&!i[r]||!D(s,z))return null}return n}const K=Object.create(null);function wt(t,n){return{provider:t,prefix:n,icons:Object.create(null),missing:new Set}}function S(t,n){const o=K[t]||(K[t]=Object.create(null));return o[n]||(o[n]=wt(t,n))}function ft(t,n){return lt(n)?ct(n,(o,i)=>{i?t.icons[o]=i:t.missing.add(o)}):[]}function vt(t,n,o){try{if(typeof o.body=="string")return t.icons[n]={...o},!0}catch{}return!1}let j=!1;function ut(t){return typeof t=="boolean"&&(j=t),j}function kt(t){const n=typeof t=="string"?M(t,!0,j):t;if(n){const o=S(n.provider,n.prefix),i=n.name;return o.icons[i]||(o.missing.has(i)?null:void 0)}}function St(t,n){const o=M(t,!0,j);if(!o)return!1;const i=S(o.provider,o.prefix);return n?vt(i,o.name,n):(i.missing.add(o.name),!0)}function Tt(t,n){if(typeof t!="object")return!1;if(typeof n!="string"&&(n=t.provider||""),j&&!n&&!t.prefix){let e=!1;return lt(t)&&(t.prefix="",ct(t,(s,r)=>{St(s,r)&&(e=!0)})),e}const o=t.prefix;if(!L({prefix:o,name:"a"}))return!1;const i=S(n,o);return!!ft(i,t)}const at=Object.freeze({width:null,height:null}),dt=Object.freeze({...at,...A}),Ct=/(-?[0-9.]*[0-9]+[0-9.]*)/g,Pt=/^-?[0-9.]*[0-9]+[0-9.]*$/g;function W(t,n,o){if(n===1)return t;if(o=o||100,typeof t=="number")return Math.ceil(t*n*o)/o;if(typeof t!="string")return t;const i=t.split(Ct);if(i===null||!i.length)return t;const e=[];let s=i.shift(),r=Pt.test(s);for(;;){if(r){const c=parseFloat(s);isNaN(c)?e.push(s):e.push(Math.ceil(c*n*o)/o)}else e.push(s);if(s=i.shift(),s===void 0)return e.join("");r=!r}}function jt(t,n="defs"){let o="";const i=t.indexOf("<"+n);for(;i>=0;){const e=t.indexOf(">",i),s=t.indexOf("</"+n);if(e===-1||s===-1)break;const r=t.indexOf(">",s);if(r===-1)break;o+=t.slice(e+1,s).trim(),t=t.slice(0,i).trim()+t.slice(r+1)}return{defs:o,content:t}}function Et(t,n){return t?"<defs>"+t+"</defs>"+n:n}function Lt(t,n,o){const i=jt(t);return Et(i.defs,n+i.content+o)}const Ft=t=>t==="unset"||t==="undefined"||t==="none";function Ot(t,n){const o={..._,...t},i={...dt,...n},e={left:o.left,top:o.top,width:o.width,height:o.height};let s=o.body;[o,i].forEach(g=>{const u=[],k=g.hFlip,w=g.vFlip;let x=g.rotate;k?w?x+=2:(u.push("translate("+(e.width+e.left).toString()+" "+(0-e.top).toString()+")"),u.push("scale(-1 1)"),e.top=e.left=0):w&&(u.push("translate("+(0-e.left).toString()+" "+(e.height+e.top).toString()+")"),u.push("scale(1 -1)"),e.top=e.left=0);let y;switch(x<0&&(x-=Math.floor(x/4)*4),x=x%4,x){case 1:y=e.height/2+e.top,u.unshift("rotate(90 "+y.toString()+" "+y.toString()+")");break;case 2:u.unshift("rotate(180 "+(e.width/2+e.left).toString()+" "+(e.height/2+e.top).toString()+")");break;case 3:y=e.width/2+e.left,u.unshift("rotate(-90 "+y.toString()+" "+y.toString()+")");break}x%2===1&&(e.left!==e.top&&(y=e.left,e.left=e.top,e.top=y),e.width!==e.height&&(y=e.width,e.width=e.height,e.height=y)),u.length&&(s=Lt(s,'<g transform="'+u.join(" ")+'">',"</g>"))});const r=i.width,c=i.height,l=e.width,f=e.height;let a,d;r===null?(d=c===null?"1em":c==="auto"?f:c,a=W(d,l/f)):(a=r==="auto"?l:r,d=c===null?W(a,f/l):c==="auto"?f:c);const p={},m=(g,u)=>{Ft(u)||(p[g]=u.toString())};m("width",a),m("height",d);const I=[e.left,e.top,l,f];return p.viewBox=I.join(" "),{attributes:p,viewBox:I,body:s}}const At=/\sid="(\S+)"/g,Mt="IconifyId"+Date.now().toString(16)+(Math.random()*16777216|0).toString(16);let _t=0;function Nt(t,n=Mt){const o=[];let i;for(;i=At.exec(t);)o.push(i[1]);if(!o.length)return t;const e="suffix"+(Math.random()*16777216|Date.now()).toString(16);return o.forEach(s=>{const r=typeof n=="function"?n(s):n+(_t++).toString(),c=s.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");t=t.replace(new RegExp('([#;"])('+c+')([")]|\\.[a-z])',"g"),"$1"+r+e+"$3")}),t=t.replace(new RegExp(e,"g"),""),t}const Q=Object.create(null);function Dt(t,n){Q[t]=n}function $(t){return Q[t]||Q[""]}function U(t){let n;if(typeof t.resources=="string")n=[t.resources];else if(n=t.resources,!(n instanceof Array)||!n.length)return null;return{resources:n,path:t.path||"/",maxURL:t.maxURL||500,rotate:t.rotate||750,timeout:t.timeout||5e3,random:t.random===!0,index:t.index||0,dataAfterTimeout:t.dataAfterTimeout!==!1}}const H=Object.create(null),C=["https://api.simplesvg.com","https://api.unisvg.com"],F=[];for(;C.length>0;)C.length===1||Math.random()>.5?F.push(C.shift()):F.push(C.pop());H[""]=U({resources:["https://api.iconify.design"].concat(F)});function Rt(t,n){const o=U(n);return o===null?!1:(H[t]=o,!0)}function V(t){return H[t]}const zt=()=>{let t;try{if(t=fetch,typeof t=="function")return t}catch{}};let J=zt();function Qt(t,n){const o=V(t);if(!o)return 0;let i;if(!o.maxURL)i=0;else{let e=0;o.resources.forEach(r=>{e=Math.max(e,r.length)});const s=n+".json?icons=";i=o.maxURL-e-o.path.length-s.length}return i}function $t(t){return t===404}const qt=(t,n,o)=>{const i=[],e=Qt(t,n),s="icons";let r={type:s,provider:t,prefix:n,icons:[]},c=0;return o.forEach((l,f)=>{c+=l.length+1,c>=e&&f>0&&(i.push(r),r={type:s,provider:t,prefix:n,icons:[]},c=l.length),r.icons.push(l)}),i.push(r),i};function Ut(t){if(typeof t=="string"){const n=V(t);if(n)return n.path}return"/"}const Ht=(t,n,o)=>{if(!J){o("abort",424);return}let i=Ut(n.provider);switch(n.type){case"icons":{const s=n.prefix,c=n.icons.join(","),l=new URLSearchParams({icons:c});i+=s+".json?"+l.toString();break}case"custom":{const s=n.uri;i+=s.slice(0,1)==="/"?s.slice(1):s;break}default:o("abort",400);return}let e=503;J(t+i).then(s=>{const r=s.status;if(r!==200){setTimeout(()=>{o($t(r)?"abort":"next",r)});return}return e=501,s.json()}).then(s=>{if(typeof s!="object"||s===null){setTimeout(()=>{s===404?o("abort",s):o("next",e)});return}setTimeout(()=>{o("success",s)})}).catch(()=>{o("next",e)})},Vt={prepare:qt,send:Ht};function Gt(t){const n={loaded:[],missing:[],pending:[]},o=Object.create(null);t.sort((e,s)=>e.provider!==s.provider?e.provider.localeCompare(s.provider):e.prefix!==s.prefix?e.prefix.localeCompare(s.prefix):e.name.localeCompare(s.name));let i={provider:"",prefix:"",name:""};return t.forEach(e=>{if(i.name===e.name&&i.prefix===e.prefix&&i.provider===e.provider)return;i=e;const s=e.provider,r=e.prefix,c=e.name,l=o[s]||(o[s]=Object.create(null)),f=l[r]||(l[r]=S(s,r));let a;c in f.icons?a=n.loaded:r===""||f.missing.has(c)?a=n.missing:a=n.pending;const d={provider:s,prefix:r,name:c};a.push(d)}),n}function ht(t,n){t.forEach(o=>{const i=o.loaderCallbacks;i&&(o.loaderCallbacks=i.filter(e=>e.id!==n))})}function Bt(t){t.pendingCallbacksFlag||(t.pendingCallbacksFlag=!0,setTimeout(()=>{t.pendingCallbacksFlag=!1;const n=t.loaderCallbacks?t.loaderCallbacks.slice(0):[];if(!n.length)return;let o=!1;const i=t.provider,e=t.prefix;n.forEach(s=>{const r=s.icons,c=r.pending.length;r.pending=r.pending.filter(l=>{if(l.prefix!==e)return!0;const f=l.name;if(t.icons[f])r.loaded.push({provider:i,prefix:e,name:f});else if(t.missing.has(f))r.missing.push({provider:i,prefix:e,name:f});else return o=!0,!0;return!1}),r.pending.length!==c&&(o||ht([t],s.id),s.callback(r.loaded.slice(0),r.missing.slice(0),r.pending.slice(0),s.abort))})}))}let Kt=0;function Wt(t,n,o){const i=Kt++,e=ht.bind(null,o,i);if(!n.pending.length)return e;const s={id:i,icons:n,callback:t,abort:e};return o.forEach(r=>{(r.loaderCallbacks||(r.loaderCallbacks=[])).push(s)}),e}function Jt(t,n=!0,o=!1){const i=[];return t.forEach(e=>{const s=typeof e=="string"?M(e,n,o):e;s&&i.push(s)}),i}var Xt={resources:[],index:0,timeout:2e3,rotate:750,random:!1,dataAfterTimeout:!1};function Yt(t,n,o,i){const e=t.resources.length,s=t.random?Math.floor(Math.random()*e):t.index;let r;if(t.random){let h=t.resources.slice(0);for(r=[];h.length>1;){const b=Math.floor(Math.random()*h.length);r.push(h[b]),h=h.slice(0,b).concat(h.slice(b+1))}r=r.concat(h)}else r=t.resources.slice(s).concat(t.resources.slice(0,s));const c=Date.now();let l="pending",f=0,a,d=null,p=[],m=[];typeof i=="function"&&m.push(i);function I(){d&&(clearTimeout(d),d=null)}function g(){l==="pending"&&(l="aborted"),I(),p.forEach(h=>{h.status==="pending"&&(h.status="aborted")}),p=[]}function u(h,b){b&&(m=[]),typeof h=="function"&&m.push(h)}function k(){return{startTime:c,payload:n,status:l,queriesSent:f,queriesPending:p.length,subscribe:u,abort:g}}function w(){l="failed",m.forEach(h=>{h(void 0,a)})}function x(){p.forEach(h=>{h.status==="pending"&&(h.status="aborted")}),p=[]}function y(h,b,T){const E=b!=="success";switch(p=p.filter(v=>v!==h),l){case"pending":break;case"failed":if(E||!t.dataAfterTimeout)return;break;default:return}if(b==="abort"){a=T,w();return}if(E){a=T,p.length||(r.length?N():w());return}if(I(),x(),!t.random){const v=t.resources.indexOf(h.resource);v!==-1&&v!==t.index&&(t.index=v)}l="completed",m.forEach(v=>{v(T)})}function N(){if(l!=="pending")return;I();const h=r.shift();if(h===void 0){if(p.length){d=setTimeout(()=>{I(),l==="pending"&&(x(),w())},t.timeout);return}w();return}const b={status:"pending",resource:h,callback:(T,E)=>{y(b,T,E)}};p.push(b),f++,d=setTimeout(N,t.rotate),o(h,n,b.callback)}return setTimeout(N),k}function pt(t){const n={...Xt,...t};let o=[];function i(){o=o.filter(c=>c().status==="pending")}function e(c,l,f){const a=Yt(n,c,l,(d,p)=>{i(),f&&f(d,p)});return o.push(a),a}function s(c){return o.find(l=>c(l))||null}return{query:e,find:s,setIndex:c=>{n.index=c},getIndex:()=>n.index,cleanup:i}}function X(){}const R=Object.create(null);function Zt(t){if(!R[t]){const n=V(t);if(!n)return;const o=pt(n),i={config:n,redundancy:o};R[t]=i}return R[t]}function te(t,n,o){let i,e;if(typeof t=="string"){const s=$(t);if(!s)return o(void 0,424),X;e=s.send;const r=Zt(t);r&&(i=r.redundancy)}else{const s=U(t);if(s){i=pt(s);const r=t.resources?t.resources[0]:"",c=$(r);c&&(e=c.send)}}return!i||!e?(o(void 0,424),X):i.query(n,e,o)().abort}function Y(){}function ee(t){t.iconsLoaderFlag||(t.iconsLoaderFlag=!0,setTimeout(()=>{t.iconsLoaderFlag=!1,Bt(t)}))}function ne(t){const n=[],o=[];return t.forEach(i=>{(i.match(st)?n:o).push(i)}),{valid:n,invalid:o}}function P(t,n,o){function i(){const e=t.pendingIcons;n.forEach(s=>{e&&e.delete(s),t.icons[s]||t.missing.add(s)})}if(o&&typeof o=="object")try{if(!ft(t,o).length){i();return}}catch(e){console.error(e)}i(),ee(t)}function Z(t,n){t instanceof Promise?t.then(o=>{n(o)}).catch(()=>{n(null)}):n(t)}function oe(t,n){t.iconsToLoad?t.iconsToLoad=t.iconsToLoad.concat(n).sort():t.iconsToLoad=n,t.iconsQueueFlag||(t.iconsQueueFlag=!0,setTimeout(()=>{t.iconsQueueFlag=!1;const{provider:o,prefix:i}=t,e=t.iconsToLoad;if(delete t.iconsToLoad,!e||!e.length)return;const s=t.loadIcon;if(t.loadIcons&&(e.length>1||!s)){Z(t.loadIcons(e,i,o),a=>{P(t,e,a)});return}if(s){e.forEach(a=>{const d=s(a,i,o);Z(d,p=>{const m=p?{prefix:i,icons:{[a]:p}}:null;P(t,[a],m)})});return}const{valid:r,invalid:c}=ne(e);if(c.length&&P(t,c,null),!r.length)return;const l=i.match(st)?$(o):null;if(!l){P(t,r,null);return}l.prepare(o,i,r).forEach(a=>{te(o,a,d=>{P(t,a.icons,d)})})}))}const ie=(t,n)=>{const o=Jt(t,!0,ut()),i=Gt(o);if(!i.pending.length){let l=!0;return n&&setTimeout(()=>{l&&n(i.loaded,i.missing,i.pending,Y)}),()=>{l=!1}}const e=Object.create(null),s=[];let r,c;return i.pending.forEach(l=>{const{provider:f,prefix:a}=l;if(a===c&&f===r)return;r=f,c=a,s.push(S(f,a));const d=e[f]||(e[f]=Object.create(null));d[a]||(d[a]=[])}),i.pending.forEach(l=>{const{provider:f,prefix:a,name:d}=l,p=S(f,a),m=p.pendingIcons||(p.pendingIcons=new Set);m.has(d)||(m.add(d),e[f][a].push(d))}),s.forEach(l=>{const f=e[l.provider][l.prefix];f.length&&oe(l,f)}),n?Wt(n,i,s):Y};function se(t,n){const o={...t};for(const i in n){const e=n[i],s=typeof e;i in at?(e===null||e&&(s==="string"||s==="number"))&&(o[i]=e):s===typeof o[i]&&(o[i]=i==="rotate"?e%4:e)}return o}const re=/[\s,]+/;function ce(t,n){n.split(re).forEach(o=>{switch(o.trim()){case"horizontal":t.hFlip=!0;break;case"vertical":t.vFlip=!0;break}})}function le(t,n=0){const o=t.replace(/^-?[0-9.]*/,"");function i(e){for(;e<0;)e+=4;return e%4}if(o===""){const e=parseInt(t);return isNaN(e)?0:i(e)}else if(o!==t){let e=0;switch(o){case"%":e=25;break;case"deg":e=90}if(e){let s=parseFloat(t.slice(0,t.length-o.length));return isNaN(s)?0:(s=s/e,s%1===0?i(s):0)}}return n}function fe(t,n){let o=t.indexOf("xlink:")===-1?"":' xmlns:xlink="http://www.w3.org/1999/xlink"';for(const i in n)o+=" "+i+'="'+n[i]+'"';return'<svg xmlns="http://www.w3.org/2000/svg"'+o+">"+t+"</svg>"}function ue(t){return t.replace(/"/g,"'").replace(/%/g,"%25").replace(/#/g,"%23").replace(/</g,"%3C").replace(/>/g,"%3E").replace(/\s+/g," ")}function ae(t){return"data:image/svg+xml,"+ue(t)}function de(t){return'url("'+ae(t)+'")'}const tt={...dt,inline:!1},he={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink","aria-hidden":!0,role:"img"},pe={display:"inline-block"},q={backgroundColor:"currentColor"},gt={backgroundColor:"transparent"},et={Image:"var(--svg)",Repeat:"no-repeat",Size:"100% 100%"},nt={webkitMask:q,mask:q,background:gt};for(const t in nt){const n=nt[t];for(const o in et)n[t+o]=et[o]}const O={};["horizontal","vertical"].forEach(t=>{const n=t.slice(0,1)+"Flip";O[t+"-flip"]=n,O[t.slice(0,1)+"-flip"]=n,O[t+"Flip"]=n});function ot(t){return t+(t.match(/^[-0-9.]+$/)?"px":"")}const it=(t,n)=>{const o=se(tt,n),i={...he},e=n.mode||"svg",s={},r=n.style,c=typeof r=="object"&&!(r instanceof Array)?r:{};for(let g in n){const u=n[g];if(u!==void 0)switch(g){case"icon":case"style":case"onLoad":case"mode":case"ssr":break;case"inline":case"hFlip":case"vFlip":o[g]=u===!0||u==="true"||u===1;break;case"flip":typeof u=="string"&&ce(o,u);break;case"color":s.color=u;break;case"rotate":typeof u=="string"?o[g]=le(u):typeof u=="number"&&(o[g]=u);break;case"ariaHidden":case"aria-hidden":u!==!0&&u!=="true"&&delete i["aria-hidden"];break;default:{const k=O[g];k?(u===!0||u==="true"||u===1)&&(o[k]=!0):tt[g]===void 0&&(i[g]=u)}}}const l=Ot(t,o),f=l.attributes;if(o.inline&&(s.verticalAlign="-0.125em"),e==="svg"){i.style={...s,...c},Object.assign(i,f);let g=0,u=n.id;return typeof u=="string"&&(u=u.replace(/-/g,"_")),i.innerHTML=Nt(l.body,u?()=>u+"ID"+g++:"iconifyVue"),G("svg",i)}const{body:a,width:d,height:p}=t,m=e==="mask"||(e==="bg"?!1:a.indexOf("currentColor")!==-1),I=fe(a,{...f,width:d+"",height:p+""});return i.style={...s,"--svg":de(I),width:ot(f.width),height:ot(f.height),...pe,...m?q:gt,...c},G("span",i)};ut(!0);Dt("",Vt);if(typeof document<"u"&&typeof window<"u"){const t=window;if(t.IconifyPreload!==void 0){const n=t.IconifyPreload,o="Invalid IconifyPreload syntax.";typeof n=="object"&&n!==null&&(n instanceof Array?n:[n]).forEach(i=>{try{(typeof i!="object"||i===null||i instanceof Array||typeof i.icons!="object"||typeof i.prefix!="string"||!Tt(i))&&console.error(o)}catch{console.error(o)}})}if(t.IconifyProviders!==void 0){const n=t.IconifyProviders;if(typeof n=="object"&&n!==null)for(let o in n){const i="IconifyProviders["+o+"] is invalid.";try{const e=n[o];if(typeof e!="object"||!e||e.resources===void 0)continue;Rt(o,e)||console.error(i)}catch{console.error(i)}}}}const ge={..._,body:""},ye=mt({inheritAttrs:!1,data(){return{_name:"",_loadingIcon:null,iconMounted:!1,counter:0}},mounted(){this.iconMounted=!0},unmounted(){this.abortLoading()},methods:{abortLoading(){this._loadingIcon&&(this._loadingIcon.abort(),this._loadingIcon=null)},getIcon(t,n,o){if(typeof t=="object"&&t!==null&&typeof t.body=="string")return this._name="",this.abortLoading(),{data:t};let i;if(typeof t!="string"||(i=M(t,!1,!0))===null)return this.abortLoading(),null;let e=kt(i);if(!e)return(!this._loadingIcon||this._loadingIcon.name!==t)&&(this.abortLoading(),this._name="",e!==null&&(this._loadingIcon={name:t,abort:ie([i],()=>{this.counter++})})),null;if(this.abortLoading(),this._name!==t&&(this._name=t,n&&n(t)),o){e=Object.assign({},e);const r=o(e.body,i.name,i.prefix,i.provider);typeof r=="string"&&(e.body=r)}const s=["iconify"];return i.prefix!==""&&s.push("iconify--"+i.prefix),i.provider!==""&&s.push("iconify--"+i.provider),{data:e,classes:s}}},render(){this.counter;const t=this.$attrs,n=this.iconMounted||t.ssr?this.getIcon(t.icon,t.onLoad,t.customise):null;if(!n)return it(ge,t);let o=t;return n.classes&&(o={...t,class:(typeof t.class=="string"?t.class+" ":"")+n.classes.join(" ")}),it({..._,...n.data},o)}}),be=(t,n)=>{const o=t.__vccOpts||t;for(const[i,e]of n)o[i]=e;return o};export{ye as I,be as _};
========
import{d as mt,O as G}from"./index-BSo9eWK3.js";const st=/^[a-z0-9]+(-[a-z0-9]+)*$/,M=(t,n,o,i="")=>{const e=t.split(":");if(t.slice(0,1)==="@"){if(e.length<2||e.length>3)return null;i=e.shift().slice(1)}if(e.length>3||!e.length)return null;if(e.length>1){const c=e.pop(),l=e.pop(),f={provider:e.length>0?e[0]:i,prefix:l,name:c};return n&&!L(f)?null:f}const s=e[0],r=s.split("-");if(r.length>1){const c={provider:i,prefix:r.shift(),name:r.join("-")};return n&&!L(c)?null:c}if(o&&i===""){const c={provider:i,prefix:"",name:s};return n&&!L(c,o)?null:c}return null},L=(t,n)=>t?!!((n&&t.prefix===""||t.prefix)&&t.name):!1,rt=Object.freeze({left:0,top:0,width:16,height:16}),A=Object.freeze({rotate:0,vFlip:!1,hFlip:!1}),_=Object.freeze({...rt,...A}),z=Object.freeze({..._,body:"",hidden:!1});function yt(t,n){const o={};!t.hFlip!=!n.hFlip&&(o.hFlip=!0),!t.vFlip!=!n.vFlip&&(o.vFlip=!0);const i=((t.rotate||0)+(n.rotate||0))%4;return i&&(o.rotate=i),o}function B(t,n){const o=yt(t,n);for(const i in z)i in A?i in t&&!(i in o)&&(o[i]=A[i]):i in n?o[i]=n[i]:i in t&&(o[i]=t[i]);return o}function bt(t,n){const o=t.icons,i=t.aliases||Object.create(null),e=Object.create(null);function s(r){if(o[r])return e[r]=[];if(!(r in e)){e[r]=null;const c=i[r]&&i[r].parent,l=c&&s(c);l&&(e[r]=[c].concat(l))}return e[r]}return Object.keys(o).concat(Object.keys(i)).forEach(s),e}function xt(t,n,o){const i=t.icons,e=t.aliases||Object.create(null);let s={};function r(c){s=B(i[c]||e[c],s)}return r(n),o.forEach(r),B(t,s)}function ct(t,n){const o=[];if(typeof t!="object"||typeof t.icons!="object")return o;t.not_found instanceof Array&&t.not_found.forEach(e=>{n(e,null),o.push(e)});const i=bt(t);for(const e in i){const s=i[e];s&&(n(e,xt(t,e,s)),o.push(e))}return o}const It={provider:"",aliases:{},not_found:{},...rt};function D(t,n){for(const o in n)if(o in t&&typeof t[o]!=typeof n[o])return!1;return!0}function lt(t){if(typeof t!="object"||t===null)return null;const n=t;if(typeof n.prefix!="string"||!t.icons||typeof t.icons!="object"||!D(t,It))return null;const o=n.icons;for(const e in o){const s=o[e];if(!e||typeof s.body!="string"||!D(s,z))return null}const i=n.aliases||Object.create(null);for(const e in i){const s=i[e],r=s.parent;if(!e||typeof r!="string"||!o[r]&&!i[r]||!D(s,z))return null}return n}const K=Object.create(null);function wt(t,n){return{provider:t,prefix:n,icons:Object.create(null),missing:new Set}}function S(t,n){const o=K[t]||(K[t]=Object.create(null));return o[n]||(o[n]=wt(t,n))}function ft(t,n){return lt(n)?ct(n,(o,i)=>{i?t.icons[o]=i:t.missing.add(o)}):[]}function vt(t,n,o){try{if(typeof o.body=="string")return t.icons[n]={...o},!0}catch{}return!1}let j=!1;function ut(t){return typeof t=="boolean"&&(j=t),j}function kt(t){const n=typeof t=="string"?M(t,!0,j):t;if(n){const o=S(n.provider,n.prefix),i=n.name;return o.icons[i]||(o.missing.has(i)?null:void 0)}}function St(t,n){const o=M(t,!0,j);if(!o)return!1;const i=S(o.provider,o.prefix);return n?vt(i,o.name,n):(i.missing.add(o.name),!0)}function Tt(t,n){if(typeof t!="object")return!1;if(typeof n!="string"&&(n=t.provider||""),j&&!n&&!t.prefix){let e=!1;return lt(t)&&(t.prefix="",ct(t,(s,r)=>{St(s,r)&&(e=!0)})),e}const o=t.prefix;if(!L({prefix:o,name:"a"}))return!1;const i=S(n,o);return!!ft(i,t)}const at=Object.freeze({width:null,height:null}),dt=Object.freeze({...at,...A}),Ct=/(-?[0-9.]*[0-9]+[0-9.]*)/g,Pt=/^-?[0-9.]*[0-9]+[0-9.]*$/g;function W(t,n,o){if(n===1)return t;if(o=o||100,typeof t=="number")return Math.ceil(t*n*o)/o;if(typeof t!="string")return t;const i=t.split(Ct);if(i===null||!i.length)return t;const e=[];let s=i.shift(),r=Pt.test(s);for(;;){if(r){const c=parseFloat(s);isNaN(c)?e.push(s):e.push(Math.ceil(c*n*o)/o)}else e.push(s);if(s=i.shift(),s===void 0)return e.join("");r=!r}}function jt(t,n="defs"){let o="";const i=t.indexOf("<"+n);for(;i>=0;){const e=t.indexOf(">",i),s=t.indexOf("</"+n);if(e===-1||s===-1)break;const r=t.indexOf(">",s);if(r===-1)break;o+=t.slice(e+1,s).trim(),t=t.slice(0,i).trim()+t.slice(r+1)}return{defs:o,content:t}}function Et(t,n){return t?"<defs>"+t+"</defs>"+n:n}function Lt(t,n,o){const i=jt(t);return Et(i.defs,n+i.content+o)}const Ot=t=>t==="unset"||t==="undefined"||t==="none";function Ft(t,n){const o={..._,...t},i={...dt,...n},e={left:o.left,top:o.top,width:o.width,height:o.height};let s=o.body;[o,i].forEach(g=>{const u=[],k=g.hFlip,w=g.vFlip;let x=g.rotate;k?w?x+=2:(u.push("translate("+(e.width+e.left).toString()+" "+(0-e.top).toString()+")"),u.push("scale(-1 1)"),e.top=e.left=0):w&&(u.push("translate("+(0-e.left).toString()+" "+(e.height+e.top).toString()+")"),u.push("scale(1 -1)"),e.top=e.left=0);let y;switch(x<0&&(x-=Math.floor(x/4)*4),x=x%4,x){case 1:y=e.height/2+e.top,u.unshift("rotate(90 "+y.toString()+" "+y.toString()+")");break;case 2:u.unshift("rotate(180 "+(e.width/2+e.left).toString()+" "+(e.height/2+e.top).toString()+")");break;case 3:y=e.width/2+e.left,u.unshift("rotate(-90 "+y.toString()+" "+y.toString()+")");break}x%2===1&&(e.left!==e.top&&(y=e.left,e.left=e.top,e.top=y),e.width!==e.height&&(y=e.width,e.width=e.height,e.height=y)),u.length&&(s=Lt(s,'<g transform="'+u.join(" ")+'">',"</g>"))});const r=i.width,c=i.height,l=e.width,f=e.height;let a,d;r===null?(d=c===null?"1em":c==="auto"?f:c,a=W(d,l/f)):(a=r==="auto"?l:r,d=c===null?W(a,f/l):c==="auto"?f:c);const p={},m=(g,u)=>{Ot(u)||(p[g]=u.toString())};m("width",a),m("height",d);const I=[e.left,e.top,l,f];return p.viewBox=I.join(" "),{attributes:p,viewBox:I,body:s}}const At=/\sid="(\S+)"/g,Mt="IconifyId"+Date.now().toString(16)+(Math.random()*16777216|0).toString(16);let _t=0;function Nt(t,n=Mt){const o=[];let i;for(;i=At.exec(t);)o.push(i[1]);if(!o.length)return t;const e="suffix"+(Math.random()*16777216|Date.now()).toString(16);return o.forEach(s=>{const r=typeof n=="function"?n(s):n+(_t++).toString(),c=s.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");t=t.replace(new RegExp('([#;"])('+c+')([")]|\\.[a-z])',"g"),"$1"+r+e+"$3")}),t=t.replace(new RegExp(e,"g"),""),t}const Q=Object.create(null);function Dt(t,n){Q[t]=n}function $(t){return Q[t]||Q[""]}function U(t){let n;if(typeof t.resources=="string")n=[t.resources];else if(n=t.resources,!(n instanceof Array)||!n.length)return null;return{resources:n,path:t.path||"/",maxURL:t.maxURL||500,rotate:t.rotate||750,timeout:t.timeout||5e3,random:t.random===!0,index:t.index||0,dataAfterTimeout:t.dataAfterTimeout!==!1}}const H=Object.create(null),C=["https://api.simplesvg.com","https://api.unisvg.com"],O=[];for(;C.length>0;)C.length===1||Math.random()>.5?O.push(C.shift()):O.push(C.pop());H[""]=U({resources:["https://api.iconify.design"].concat(O)});function Rt(t,n){const o=U(n);return o===null?!1:(H[t]=o,!0)}function V(t){return H[t]}const zt=()=>{let t;try{if(t=fetch,typeof t=="function")return t}catch{}};let J=zt();function Qt(t,n){const o=V(t);if(!o)return 0;let i;if(!o.maxURL)i=0;else{let e=0;o.resources.forEach(r=>{e=Math.max(e,r.length)});const s=n+".json?icons=";i=o.maxURL-e-o.path.length-s.length}return i}function $t(t){return t===404}const qt=(t,n,o)=>{const i=[],e=Qt(t,n),s="icons";let r={type:s,provider:t,prefix:n,icons:[]},c=0;return o.forEach((l,f)=>{c+=l.length+1,c>=e&&f>0&&(i.push(r),r={type:s,provider:t,prefix:n,icons:[]},c=l.length),r.icons.push(l)}),i.push(r),i};function Ut(t){if(typeof t=="string"){const n=V(t);if(n)return n.path}return"/"}const Ht=(t,n,o)=>{if(!J){o("abort",424);return}let i=Ut(n.provider);switch(n.type){case"icons":{const s=n.prefix,c=n.icons.join(","),l=new URLSearchParams({icons:c});i+=s+".json?"+l.toString();break}case"custom":{const s=n.uri;i+=s.slice(0,1)==="/"?s.slice(1):s;break}default:o("abort",400);return}let e=503;J(t+i).then(s=>{const r=s.status;if(r!==200){setTimeout(()=>{o($t(r)?"abort":"next",r)});return}return e=501,s.json()}).then(s=>{if(typeof s!="object"||s===null){setTimeout(()=>{s===404?o("abort",s):o("next",e)});return}setTimeout(()=>{o("success",s)})}).catch(()=>{o("next",e)})},Vt={prepare:qt,send:Ht};function Gt(t){const n={loaded:[],missing:[],pending:[]},o=Object.create(null);t.sort((e,s)=>e.provider!==s.provider?e.provider.localeCompare(s.provider):e.prefix!==s.prefix?e.prefix.localeCompare(s.prefix):e.name.localeCompare(s.name));let i={provider:"",prefix:"",name:""};return t.forEach(e=>{if(i.name===e.name&&i.prefix===e.prefix&&i.provider===e.provider)return;i=e;const s=e.provider,r=e.prefix,c=e.name,l=o[s]||(o[s]=Object.create(null)),f=l[r]||(l[r]=S(s,r));let a;c in f.icons?a=n.loaded:r===""||f.missing.has(c)?a=n.missing:a=n.pending;const d={provider:s,prefix:r,name:c};a.push(d)}),n}function ht(t,n){t.forEach(o=>{const i=o.loaderCallbacks;i&&(o.loaderCallbacks=i.filter(e=>e.id!==n))})}function Bt(t){t.pendingCallbacksFlag||(t.pendingCallbacksFlag=!0,setTimeout(()=>{t.pendingCallbacksFlag=!1;const n=t.loaderCallbacks?t.loaderCallbacks.slice(0):[];if(!n.length)return;let o=!1;const i=t.provider,e=t.prefix;n.forEach(s=>{const r=s.icons,c=r.pending.length;r.pending=r.pending.filter(l=>{if(l.prefix!==e)return!0;const f=l.name;if(t.icons[f])r.loaded.push({provider:i,prefix:e,name:f});else if(t.missing.has(f))r.missing.push({provider:i,prefix:e,name:f});else return o=!0,!0;return!1}),r.pending.length!==c&&(o||ht([t],s.id),s.callback(r.loaded.slice(0),r.missing.slice(0),r.pending.slice(0),s.abort))})}))}let Kt=0;function Wt(t,n,o){const i=Kt++,e=ht.bind(null,o,i);if(!n.pending.length)return e;const s={id:i,icons:n,callback:t,abort:e};return o.forEach(r=>{(r.loaderCallbacks||(r.loaderCallbacks=[])).push(s)}),e}function Jt(t,n=!0,o=!1){const i=[];return t.forEach(e=>{const s=typeof e=="string"?M(e,n,o):e;s&&i.push(s)}),i}var Xt={resources:[],index:0,timeout:2e3,rotate:750,random:!1,dataAfterTimeout:!1};function Yt(t,n,o,i){const e=t.resources.length,s=t.random?Math.floor(Math.random()*e):t.index;let r;if(t.random){let h=t.resources.slice(0);for(r=[];h.length>1;){const b=Math.floor(Math.random()*h.length);r.push(h[b]),h=h.slice(0,b).concat(h.slice(b+1))}r=r.concat(h)}else r=t.resources.slice(s).concat(t.resources.slice(0,s));const c=Date.now();let l="pending",f=0,a,d=null,p=[],m=[];typeof i=="function"&&m.push(i);function I(){d&&(clearTimeout(d),d=null)}function g(){l==="pending"&&(l="aborted"),I(),p.forEach(h=>{h.status==="pending"&&(h.status="aborted")}),p=[]}function u(h,b){b&&(m=[]),typeof h=="function"&&m.push(h)}function k(){return{startTime:c,payload:n,status:l,queriesSent:f,queriesPending:p.length,subscribe:u,abort:g}}function w(){l="failed",m.forEach(h=>{h(void 0,a)})}function x(){p.forEach(h=>{h.status==="pending"&&(h.status="aborted")}),p=[]}function y(h,b,T){const E=b!=="success";switch(p=p.filter(v=>v!==h),l){case"pending":break;case"failed":if(E||!t.dataAfterTimeout)return;break;default:return}if(b==="abort"){a=T,w();return}if(E){a=T,p.length||(r.length?N():w());return}if(I(),x(),!t.random){const v=t.resources.indexOf(h.resource);v!==-1&&v!==t.index&&(t.index=v)}l="completed",m.forEach(v=>{v(T)})}function N(){if(l!=="pending")return;I();const h=r.shift();if(h===void 0){if(p.length){d=setTimeout(()=>{I(),l==="pending"&&(x(),w())},t.timeout);return}w();return}const b={status:"pending",resource:h,callback:(T,E)=>{y(b,T,E)}};p.push(b),f++,d=setTimeout(N,t.rotate),o(h,n,b.callback)}return setTimeout(N),k}function pt(t){const n={...Xt,...t};let o=[];function i(){o=o.filter(c=>c().status==="pending")}function e(c,l,f){const a=Yt(n,c,l,(d,p)=>{i(),f&&f(d,p)});return o.push(a),a}function s(c){return o.find(l=>c(l))||null}return{query:e,find:s,setIndex:c=>{n.index=c},getIndex:()=>n.index,cleanup:i}}function X(){}const R=Object.create(null);function Zt(t){if(!R[t]){const n=V(t);if(!n)return;const o=pt(n),i={config:n,redundancy:o};R[t]=i}return R[t]}function te(t,n,o){let i,e;if(typeof t=="string"){const s=$(t);if(!s)return o(void 0,424),X;e=s.send;const r=Zt(t);r&&(i=r.redundancy)}else{const s=U(t);if(s){i=pt(s);const r=t.resources?t.resources[0]:"",c=$(r);c&&(e=c.send)}}return!i||!e?(o(void 0,424),X):i.query(n,e,o)().abort}function Y(){}function ee(t){t.iconsLoaderFlag||(t.iconsLoaderFlag=!0,setTimeout(()=>{t.iconsLoaderFlag=!1,Bt(t)}))}function ne(t){const n=[],o=[];return t.forEach(i=>{(i.match(st)?n:o).push(i)}),{valid:n,invalid:o}}function P(t,n,o){function i(){const e=t.pendingIcons;n.forEach(s=>{e&&e.delete(s),t.icons[s]||t.missing.add(s)})}if(o&&typeof o=="object")try{if(!ft(t,o).length){i();return}}catch(e){console.error(e)}i(),ee(t)}function Z(t,n){t instanceof Promise?t.then(o=>{n(o)}).catch(()=>{n(null)}):n(t)}function oe(t,n){t.iconsToLoad?t.iconsToLoad=t.iconsToLoad.concat(n).sort():t.iconsToLoad=n,t.iconsQueueFlag||(t.iconsQueueFlag=!0,setTimeout(()=>{t.iconsQueueFlag=!1;const{provider:o,prefix:i}=t,e=t.iconsToLoad;if(delete t.iconsToLoad,!e||!e.length)return;const s=t.loadIcon;if(t.loadIcons&&(e.length>1||!s)){Z(t.loadIcons(e,i,o),a=>{P(t,e,a)});return}if(s){e.forEach(a=>{const d=s(a,i,o);Z(d,p=>{const m=p?{prefix:i,icons:{[a]:p}}:null;P(t,[a],m)})});return}const{valid:r,invalid:c}=ne(e);if(c.length&&P(t,c,null),!r.length)return;const l=i.match(st)?$(o):null;if(!l){P(t,r,null);return}l.prepare(o,i,r).forEach(a=>{te(o,a,d=>{P(t,a.icons,d)})})}))}const ie=(t,n)=>{const o=Jt(t,!0,ut()),i=Gt(o);if(!i.pending.length){let l=!0;return n&&setTimeout(()=>{l&&n(i.loaded,i.missing,i.pending,Y)}),()=>{l=!1}}const e=Object.create(null),s=[];let r,c;return i.pending.forEach(l=>{const{provider:f,prefix:a}=l;if(a===c&&f===r)return;r=f,c=a,s.push(S(f,a));const d=e[f]||(e[f]=Object.create(null));d[a]||(d[a]=[])}),i.pending.forEach(l=>{const{provider:f,prefix:a,name:d}=l,p=S(f,a),m=p.pendingIcons||(p.pendingIcons=new Set);m.has(d)||(m.add(d),e[f][a].push(d))}),s.forEach(l=>{const f=e[l.provider][l.prefix];f.length&&oe(l,f)}),n?Wt(n,i,s):Y};function se(t,n){const o={...t};for(const i in n){const e=n[i],s=typeof e;i in at?(e===null||e&&(s==="string"||s==="number"))&&(o[i]=e):s===typeof o[i]&&(o[i]=i==="rotate"?e%4:e)}return o}const re=/[\s,]+/;function ce(t,n){n.split(re).forEach(o=>{switch(o.trim()){case"horizontal":t.hFlip=!0;break;case"vertical":t.vFlip=!0;break}})}function le(t,n=0){const o=t.replace(/^-?[0-9.]*/,"");function i(e){for(;e<0;)e+=4;return e%4}if(o===""){const e=parseInt(t);return isNaN(e)?0:i(e)}else if(o!==t){let e=0;switch(o){case"%":e=25;break;case"deg":e=90}if(e){let s=parseFloat(t.slice(0,t.length-o.length));return isNaN(s)?0:(s=s/e,s%1===0?i(s):0)}}return n}function fe(t,n){let o=t.indexOf("xlink:")===-1?"":' xmlns:xlink="http://www.w3.org/1999/xlink"';for(const i in n)o+=" "+i+'="'+n[i]+'"';return'<svg xmlns="http://www.w3.org/2000/svg"'+o+">"+t+"</svg>"}function ue(t){return t.replace(/"/g,"'").replace(/%/g,"%25").replace(/#/g,"%23").replace(/</g,"%3C").replace(/>/g,"%3E").replace(/\s+/g," ")}function ae(t){return"data:image/svg+xml,"+ue(t)}function de(t){return'url("'+ae(t)+'")'}const tt={...dt,inline:!1},he={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink","aria-hidden":!0,role:"img"},pe={display:"inline-block"},q={backgroundColor:"currentColor"},gt={backgroundColor:"transparent"},et={Image:"var(--svg)",Repeat:"no-repeat",Size:"100% 100%"},nt={webkitMask:q,mask:q,background:gt};for(const t in nt){const n=nt[t];for(const o in et)n[t+o]=et[o]}const F={};["horizontal","vertical"].forEach(t=>{const n=t.slice(0,1)+"Flip";F[t+"-flip"]=n,F[t.slice(0,1)+"-flip"]=n,F[t+"Flip"]=n});function ot(t){return t+(t.match(/^[-0-9.]+$/)?"px":"")}const it=(t,n)=>{const o=se(tt,n),i={...he},e=n.mode||"svg",s={},r=n.style,c=typeof r=="object"&&!(r instanceof Array)?r:{};for(let g in n){const u=n[g];if(u!==void 0)switch(g){case"icon":case"style":case"onLoad":case"mode":case"ssr":break;case"inline":case"hFlip":case"vFlip":o[g]=u===!0||u==="true"||u===1;break;case"flip":typeof u=="string"&&ce(o,u);break;case"color":s.color=u;break;case"rotate":typeof u=="string"?o[g]=le(u):typeof u=="number"&&(o[g]=u);break;case"ariaHidden":case"aria-hidden":u!==!0&&u!=="true"&&delete i["aria-hidden"];break;default:{const k=F[g];k?(u===!0||u==="true"||u===1)&&(o[k]=!0):tt[g]===void 0&&(i[g]=u)}}}const l=Ft(t,o),f=l.attributes;if(o.inline&&(s.verticalAlign="-0.125em"),e==="svg"){i.style={...s,...c},Object.assign(i,f);let g=0,u=n.id;return typeof u=="string"&&(u=u.replace(/-/g,"_")),i.innerHTML=Nt(l.body,u?()=>u+"ID"+g++:"iconifyVue"),G("svg",i)}const{body:a,width:d,height:p}=t,m=e==="mask"||(e==="bg"?!1:a.indexOf("currentColor")!==-1),I=fe(a,{...f,width:d+"",height:p+""});return i.style={...s,"--svg":de(I),width:ot(f.width),height:ot(f.height),...pe,...m?q:gt,...c},G("span",i)};ut(!0);Dt("",Vt);if(typeof document<"u"&&typeof window<"u"){const t=window;if(t.IconifyPreload!==void 0){const n=t.IconifyPreload,o="Invalid IconifyPreload syntax.";typeof n=="object"&&n!==null&&(n instanceof Array?n:[n]).forEach(i=>{try{(typeof i!="object"||i===null||i instanceof Array||typeof i.icons!="object"||typeof i.prefix!="string"||!Tt(i))&&console.error(o)}catch{console.error(o)}})}if(t.IconifyProviders!==void 0){const n=t.IconifyProviders;if(typeof n=="object"&&n!==null)for(let o in n){const i="IconifyProviders["+o+"] is invalid.";try{const e=n[o];if(typeof e!="object"||!e||e.resources===void 0)continue;Rt(o,e)||console.error(i)}catch{console.error(i)}}}}const ge={..._,body:""},ye=mt({inheritAttrs:!1,data(){return{_name:"",_loadingIcon:null,iconMounted:!1,counter:0}},mounted(){this.iconMounted=!0},unmounted(){this.abortLoading()},methods:{abortLoading(){this._loadingIcon&&(this._loadingIcon.abort(),this._loadingIcon=null)},getIcon(t,n,o){if(typeof t=="object"&&t!==null&&typeof t.body=="string")return this._name="",this.abortLoading(),{data:t};let i;if(typeof t!="string"||(i=M(t,!1,!0))===null)return this.abortLoading(),null;let e=kt(i);if(!e)return(!this._loadingIcon||this._loadingIcon.name!==t)&&(this.abortLoading(),this._name="",e!==null&&(this._loadingIcon={name:t,abort:ie([i],()=>{this.counter++})})),null;if(this.abortLoading(),this._name!==t&&(this._name=t,n&&n(t)),o){e=Object.assign({},e);const r=o(e.body,i.name,i.prefix,i.provider);typeof r=="string"&&(e.body=r)}const s=["iconify"];return i.prefix!==""&&s.push("iconify--"+i.prefix),i.provider!==""&&s.push("iconify--"+i.provider),{data:e,classes:s}}},render(){this.counter;const t=this.$attrs,n=this.iconMounted||t.ssr?this.getIcon(t.icon,t.onLoad,t.customise):null;if(!n)return it(ge,t);let o=t;return n.classes&&(o={...t,class:(typeof t.class=="string"?t.class+" ":"")+n.classes.join(" ")}),it({..._,...n.data},o)}}),be=(t,n)=>{const o=t.__vccOpts||t;for(const[i,e]of n)o[i]=e;return o};export{ye as I,be as _};
>>>>>>>> upstream/main:spring-ai-alibaba-jmanus/src/main/resources/static/ui/assets/_plugin-vue_export-helper-B6wzie0r.js
