<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语义模型配置</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <style>
        :root {
            --primary-color: #1890ff;
            --secondary-color: #52c41a;
            --background-color: #f5f8fa;
            --card-bg: #ffffff;
            --text-color: #333333;
            --border-color: #e8e8e8;
            --shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            --radius: 8px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            line-height: 1.6;
        }

        .header {
            background-color: var(--card-bg);
            padding: 1rem;
            box-shadow: var(--shadow);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .container {
            max-width: 100%;
            margin: 0 auto;
            padding: 1rem 2rem;
        }

        .title {
            font-size: 1.6rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 0.3rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .subtitle {
            font-size: 0.95rem;
            color: #666;
            margin-bottom: 0.8rem;
        }

        .toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .search-container {
            display: flex;
            gap: 0.5rem;
            flex: 1;
            max-width: 400px;
        }

        .search-input {
            flex: 1;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            transition: all 0.3s;
            outline: none;
        }

        .search-input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--radius);
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: #40a9ff;
        }

        .btn-success {
            background-color: var(--secondary-color);
            color: white;
        }

        .btn-success:hover {
            background-color: #73d13d;
        }

        .btn-warning {
            background-color: #faad14;
            color: white;
        }

        .btn-warning:hover {
            background-color: #ffc53d;
        }

        .btn-danger {
            background-color: #ff4d4f;
            color: white;
        }

        .btn-danger:hover {
            background-color: #ff7875;
        }

        .btn-secondary {
            background-color: #f5f5f5;
            color: #666;
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background-color: #e6e6e6;
        }

        .card {
            background-color: var(--card-bg);
            border-radius: var(--radius);
            box-shadow: var(--shadow);
            overflow: hidden;
        }

        .table-container {
            overflow-x: auto;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            min-width: 1200px;
            table-layout: auto;
        }

        .table th,
        .table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .table th:last-child,
        .table td:last-child {
            width: 200px;
            min-width: 200px;
        }

        .table th {
            background-color: #fafafa;
            font-weight: 500;
            color: #666;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .table tr:hover {
            background-color: #f9f9f9;
        }

        .badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
            font-weight: 500;
            border-radius: 20px;
        }

        .badge-success {
            background-color: #f6ffed;
            color: var(--secondary-color);
        }

        .badge-secondary {
            background-color: #f5f5f5;
            color: #666;
        }

        .badge-primary {
            background-color: #e6f7ff;
            color: var(--primary-color);
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background-color: var(--card-bg);
            border-radius: var(--radius);
            width: 90%;
            max-width: 700px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 1.2rem;
            font-weight: 600;
        }

        .modal-body {
            padding: 1.5rem;
        }

        .modal-footer {
            padding: 1rem 1.5rem;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
            gap: 0.5rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            font-size: 1rem;
            transition: all 0.3s;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .form-control[type="checkbox"] {
            width: auto;
            margin-right: 0.5rem;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #999;
        }

        .close-btn:hover {
            color: #333;
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #999;
        }

        .empty-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #ccc;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: #666;
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(0, 0, 0, 0.1);
            border-top-color: var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 0.5rem;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        .nav-links {
            display: flex;
            gap: 0.8rem;
            margin-bottom: 0.5rem;
        }

        .nav-link {
            padding: 0.4rem 0.8rem;
            text-decoration: none;
            color: #666;
            border-radius: var(--radius);
            transition: all 0.3s;
            font-size: 0.95rem;
        }

        .nav-link:hover {
            background-color: #f0f5ff;
            color: var(--primary-color);
        }

        .nav-link.active {
            background-color: var(--primary-color);
            color: white;
        }

        .batch-actions {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        .dataset-filter {
            padding: 0.5rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            font-size: 0.9rem;
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
            justify-content: center;
            flex-wrap: nowrap;
            flex-direction: row;
        }

        .btn-sm {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
            border-radius: 4px;
            min-width: 60px;
            width: auto;
            white-space: nowrap;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.25rem;
            flex-shrink: 0;
            word-break: keep-all;
            overflow-wrap: normal;
        }

        @media (max-width: 768px) {
            .toolbar {
                flex-direction: column;
                align-items: stretch;
            }

            .search-container {
                max-width: none;
            }

            .batch-actions {
                flex-wrap: wrap;
            }

            .action-buttons {
                flex-direction: row;
                gap: 0.25rem;
                justify-content: center;
            }

            .btn-sm {
                min-width: 60px;
                font-size: 0.8rem;
                padding: 0.25rem 0.5rem;
            }
        }
    </style>
</head>

<body>
    <div class="header">
        <div class="container">
            <div class="title">
                <i class="bi bi-diagram-3"></i> 语义模型配置
            </div>
            <div class="subtitle">对数据集字段进行语义重新设定，提升智能体自动选择数据集和问答的准确性</div>
            <div class="nav-links">
                <a href="/nl2sql.html" class="nav-link">
                    <i class="bi bi-house"></i> 首页
                </a>
                <a href="/business-knowledge.html" class="nav-link">
                    <i class="bi bi-book"></i> 业务知识管理
                </a>
                <a href="/semantic-model.html" class="nav-link active">
                    <i class="bi bi-diagram-3"></i> 语义模型配置
                </a>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="toolbar">
            <div class="search-container">
                <input type="text" id="searchInput" class="search-input" placeholder="搜索字段名称或同义词...">
                <button id="searchBtn" class="btn btn-primary">
                    <i class="bi bi-search"></i> 搜索
                </button>
            </div>
            <div class="batch-actions">
                <select id="datasetFilter" class="dataset-filter">
                    <option value="">所有数据集</option>
                </select>
                <button id="batchEnableBtn" class="btn btn-success">
                    <i class="bi bi-check-circle"></i> 批量启用
                </button>
                <button id="batchDisableBtn" class="btn btn-warning">
                    <i class="bi bi-x-circle"></i> 批量禁用
                </button>
                <button id="addBtn" class="btn btn-primary">
                    <i class="bi bi-plus-circle"></i> 新增配置
                </button>
            </div>
        </div>

        <div class="card">
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th width="40px">
                                <input type="checkbox" id="selectAll" class="form-control">
                            </th>
                            <th>数据集ID</th>
                            <th>原始字段名</th>
                            <th>智能体字段名称</th>
                            <th>字段名称同义词</th>
                            <th>字段描述</th>
                            <th>字段类型</th>
                            <th>默认召回</th>
                            <th>启用状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="tableBody">
                        <tr>
                            <td colspan="11" class="loading">
                                <div class="spinner"></div>
                                加载中...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 新增/编辑模态框 -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">新增语义模型配置</h3>
                <button class="close-btn" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="editForm">
                    <input type="hidden" id="editId">
                    <div class="form-group">
                        <label class="form-label" for="datasetId">数据集ID *</label>
                        <input type="text" id="datasetId" class="form-control" required placeholder="如：dataset_001">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="originalFieldName">原始字段名 *</label>
                        <input type="text" id="originalFieldName" class="form-control" required
                            placeholder="如：user_age">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="agentFieldName">智能体字段名称</label>
                        <input type="text" id="agentFieldName" class="form-control" placeholder="如：用户年龄（为空时与原始字段名保持一致）">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="fieldSynonyms">字段名称同义词</label>
                        <input type="text" id="fieldSynonyms" class="form-control" placeholder="多个同义词用逗号分隔，如：年龄,岁数">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="fieldDescription">字段描述</label>
                        <textarea id="fieldDescription" class="form-control" rows="3"
                            placeholder="用于帮助对字段的理解（为空时与原始字段描述保持一致）"></textarea>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="fieldType">字段类型</label>
                        <select id="fieldType" class="form-control">
                            <option value="VARCHAR">VARCHAR</option>
                            <option value="INTEGER">INTEGER</option>
                            <option value="DECIMAL">DECIMAL</option>
                            <option value="DATE">DATE</option>
                            <option value="DATETIME">DATETIME</option>
                            <option value="TEXT">TEXT</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="originalDescription">原始字段描述</label>
                        <textarea id="originalDescription" class="form-control" rows="2"
                            placeholder="数据集中原始字段的描述"></textarea>
                    </div>
                    <div class="form-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="defaultRecall" class="form-control">
                            <label class="form-label" for="defaultRecall">默认召回</label>
                        </div>
                        <small style="color: #666; margin-left: 1.5rem;">勾选后，该字段每次提问时都会作为提示词传输给大模型</small>
                    </div>
                    <div class="form-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="enabled" class="form-control" checked>
                            <label class="form-label" for="enabled">启用状态</label>
                        </div>
                        <small style="color: #666; margin-left: 1.5rem;">勾选后，该语义模型配置将生效</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal()">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveModel()">保存</button>
            </div>
        </div>
    </div>

    <script>
        let modelList = [];
        let currentEditId = null;
        let datasets = new Set();

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function () {
            loadModelList();

            // 绑定事件
            document.getElementById('searchBtn').addEventListener('click', searchModel);
            document.getElementById('searchInput').addEventListener('keypress', function (e) {
                if (e.key === 'Enter') {
                    searchModel();
                }
            });
            document.getElementById('addBtn').addEventListener('click', showAddModal);
            document.getElementById('batchEnableBtn').addEventListener('click', () => batchUpdateSelectedItems(true));
            document.getElementById('batchDisableBtn').addEventListener('click', () => batchUpdateSelectedItems(false));
            document.getElementById('datasetFilter').addEventListener('change', filterByDataset);
            
            // 全选/取消全选
            document.getElementById('selectAll').addEventListener('change', function() {
                const checkboxes = document.querySelectorAll('.row-checkbox:not([disabled])');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
            });
            
            // 委托事件处理，监听表格中的复选框变化
            document.getElementById('tableBody').addEventListener('change', function(e) {
                if (e.target && e.target.classList.contains('row-checkbox')) {
                    updateSelectAllCheckbox();
                }
            });
        });

        // 加载模型列表
        function loadModelList(keyword = '') {
            const url = keyword ? `/api/semantic-model?keyword=${encodeURIComponent(keyword)}` : '/api/semantic-model';

            fetch(url)
                .then(response => response.json())
                .then(data => {
                    modelList = data;
                    updateDatasetFilter();
                    renderTable();
                })
                .catch(error => {
                    console.error('加载数据失败:', error);
                    document.getElementById('tableBody').innerHTML = `
                        <tr>
                            <td colspan="11" class="empty-state">
                                <div class="empty-icon"><i class="bi bi-exclamation-triangle"></i></div>
                                <div>加载数据失败，请刷新页面重试</div>
                            </td>
                        </tr>
                    `;
                });
        }

        // 更新数据集过滤器
        function updateDatasetFilter() {
            datasets.clear();
            modelList.forEach(item => {
                if (item.datasetId) {
                    datasets.add(item.datasetId);
                }
            });

            const filter = document.getElementById('datasetFilter');
            const currentValue = filter.value;
            filter.innerHTML = '<option value="">所有数据集</option>';

            Array.from(datasets).sort().forEach(datasetId => {
                const option = document.createElement('option');
                option.value = datasetId;
                option.textContent = datasetId;
                filter.appendChild(option);
            });

            filter.value = currentValue;
        }

        // 按数据集过滤
        function filterByDataset() {
            const selectedDataset = document.getElementById('datasetFilter').value;
            const filteredList = selectedDataset ?
                modelList.filter(item => item.datasetId === selectedDataset) :
                modelList;
            renderTable(filteredList);
            
            // 重置全选复选框
            document.getElementById('selectAll').checked = false;
        }

        // 渲染表格
        function renderTable(data = modelList) {
            const tbody = document.getElementById('tableBody');

            if (data.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="11" class="empty-state">
                            <div class="empty-icon"><i class="bi bi-inbox"></i></div>
                            <div>暂无数据</div>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = data.map(item => `
                <tr>
                    <td>
                        <input type="checkbox" class="form-control row-checkbox" data-id="${item.id}" ${item.datasetId === document.getElementById('datasetFilter').value || document.getElementById('datasetFilter').value === '' ? '' : 'disabled'}>
                    </td>
                    <td><span class="badge badge-primary">${item.datasetId || '-'}</span></td>
                    <td><strong>${item.originalFieldName}</strong></td>
                    <td>${item.agentFieldName || '-'}</td>
                    <td style="max-width: 200px; word-wrap: break-word;">${item.fieldSynonyms || '-'}</td>
                    <td style="max-width: 250px; word-wrap: break-word;">${item.fieldDescription || '-'}</td>
                    <td><span class="badge badge-secondary">${item.fieldType || '-'}</span></td>
                    <td>
                        <span class="badge ${item.defaultRecall ? 'badge-success' : 'badge-secondary'}">
                            ${item.defaultRecall ? '是' : '否'}
                        </span>
                    </td>
                    <td>
                        <span class="badge ${item.enabled ? 'badge-success' : 'badge-secondary'}">
                            ${item.enabled ? '启用' : '禁用'}
                        </span>
                    </td>
                    <td>${formatDateTime(item.createTime)}</td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn btn-primary btn-sm" onclick="showEditModal(${item.id})">
                                <i class="bi bi-pencil"></i> 编辑
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="deleteModel(${item.id})">
                                <i class="bi bi-trash"></i> 删除
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        // 搜索模型
        function searchModel() {
            const keyword = document.getElementById('searchInput').value.trim();
            loadModelList(keyword);
        }

        // 显示新增模态框
        function showAddModal() {
            currentEditId = null;
            document.getElementById('modalTitle').textContent = '新增语义模型配置';
            document.getElementById('editForm').reset();
            document.getElementById('editId').value = '';
            document.getElementById('enabled').checked = true;
            document.getElementById('editModal').classList.add('show');
        }

        // 显示编辑模态框
        function showEditModal(id) {
            const item = modelList.find(m => m.id === id);
            if (!item) return;

            currentEditId = id;
            document.getElementById('modalTitle').textContent = '编辑语义模型配置';
            document.getElementById('editId').value = id;
            document.getElementById('datasetId').value = item.datasetId || '';
            document.getElementById('originalFieldName').value = item.originalFieldName || '';
            document.getElementById('agentFieldName').value = item.agentFieldName || '';
            document.getElementById('fieldSynonyms').value = item.fieldSynonyms || '';
            document.getElementById('fieldDescription').value = item.fieldDescription || '';
            document.getElementById('fieldType').value = item.fieldType || 'VARCHAR';
            document.getElementById('originalDescription').value = item.originalDescription || '';
            document.getElementById('defaultRecall').checked = item.defaultRecall;
            document.getElementById('enabled').checked = item.enabled;
            document.getElementById('editModal').classList.add('show');
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('editModal').classList.remove('show');
        }

        // 保存模型
        function saveModel() {
            const form = document.getElementById('editForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const data = {
                datasetId: document.getElementById('datasetId').value.trim(),
                originalFieldName: document.getElementById('originalFieldName').value.trim(),
                agentFieldName: document.getElementById('agentFieldName').value.trim() || null,
                fieldSynonyms: document.getElementById('fieldSynonyms').value.trim() || null,
                fieldDescription: document.getElementById('fieldDescription').value.trim() || null,
                fieldType: document.getElementById('fieldType').value,
                originalDescription: document.getElementById('originalDescription').value.trim() || null,
                defaultRecall: document.getElementById('defaultRecall').checked,
                enabled: document.getElementById('enabled').checked
            };

            const url = currentEditId ? `/api/semantic-model/${currentEditId}` : '/api/semantic-model';
            const method = currentEditId ? 'PUT' : 'POST';

            fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
                .then(response => response.json())
                .then(() => {
                    closeModal();
                    loadModelList();
                })
                .catch(error => {
                    console.error('保存失败:', error);
                    alert('保存失败，请重试');
                });
        }

        // 删除模型
        function deleteModel(id) {
            if (!confirm('确定要删除这条语义模型配置吗？')) {
                return;
            }

            fetch(`/api/semantic-model/${id}`, {
                method: 'DELETE'
            })
                .then(() => {
                    loadModelList();
                })
                .catch(error => {
                    console.error('删除失败:', error);
                    alert('删除失败，请重试');
                });
        }

        // 批量更新启用状态 (旧方法，保留兼容性)
        function batchUpdateEnabled(enabled) {
            const selectedDataset = document.getElementById('datasetFilter').value;
            if (!selectedDataset) {
                alert('请先选择要操作的数据集');
                return;
            }

            const action = enabled ? '启用' : '禁用';
            if (!confirm(`确定要批量${action}数据集 "${selectedDataset}" 下的所有字段配置吗？`)) {
                return;
            }

            fetch(`/api/semantic-model/batch-enable?datasetId=${encodeURIComponent(selectedDataset)}&enabled=${enabled}`, {
                method: 'POST'
            })
                .then(() => {
                    loadModelList();
                })
                .catch(error => {
                    console.error('批量操作失败:', error);
                    alert('批量操作失败，请重试');
                });
        }
        
        // 批量更新选中项的启用状态
        function batchUpdateSelectedItems(enabled) {
            const checkboxes = document.querySelectorAll('.row-checkbox:checked');
            if (checkboxes.length === 0) {
                alert('请先选择要操作的项');
                return;
            }
            
            const selectedIds = Array.from(checkboxes).map(checkbox => checkbox.getAttribute('data-id'));
            const action = enabled ? '启用' : '禁用';
            
            if (!confirm(`确定要${action}选中的 ${selectedIds.length} 项吗？`)) {
                return;
            }
            
            // 使用Promise.all并行处理所有请求
            const promises = selectedIds.map(id => {
                const item = modelList.find(m => m.id == id);
                if (!item) return Promise.resolve();
                
                const data = {
                    datasetId: item.datasetId,
                    originalFieldName: item.originalFieldName,
                    agentFieldName: item.agentFieldName,
                    fieldSynonyms: item.fieldSynonyms,
                    fieldDescription: item.fieldDescription,
                    fieldType: item.fieldType,
                    originalDescription: item.originalDescription,
                    defaultRecall: item.defaultRecall,
                    enabled: enabled // 更新启用状态
                };
                
                return fetch(`/api/semantic-model/${id}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });
            });
            
            Promise.all(promises)
                .then(() => {
                    loadModelList();
                    alert(`批量${action}操作成功`);
                })
                .catch(error => {
                    console.error('批量操作失败:', error);
                    alert('批量操作失败，请重试');
                });
        }
        
        // 更新全选复选框状态
        function updateSelectAllCheckbox() {
            const checkboxes = document.querySelectorAll('.row-checkbox:not([disabled])');
            const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');
            
            if (checkboxes.length > 0) {
                document.getElementById('selectAll').checked = checkboxes.length === checkedBoxes.length;
                document.getElementById('selectAll').indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < checkboxes.length;
            }
        }

        // 格式化日期时间
        function formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return '-';
            const date = new Date(dateTimeStr);
            return date.toLocaleString('zh-CN');
        }

        // 点击模态框外部关闭
        document.getElementById('editModal').addEventListener('click', function (e) {
            if (e.target === this) {
                closeModal();
            }
        });
    </script>
</body>

</html>